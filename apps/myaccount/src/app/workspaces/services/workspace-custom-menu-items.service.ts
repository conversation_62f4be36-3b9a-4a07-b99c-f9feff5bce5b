import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceCustomMenuItem } from '../../entities/workspace-custom-menu-item.entity';
import { CreateWorkspaceCustomMenuItemDto } from '../dto/create-workspace-custom-menu-item.dto';

@Injectable()
export class WorkspaceCustomMenuItemsService {
  constructor(
    @InjectRepository(WorkspaceCustomMenuItem)
    private readonly workspaceCustomMenuItemRepository: Repository<WorkspaceCustomMenuItem>,
  ) {}

  async create(createWorkspaceCustomMenuItemDto: CreateWorkspaceCustomMenuItemDto): Promise<WorkspaceCustomMenuItem> {
    const customMenuItem = this.workspaceCustomMenuItemRepository.create(createWorkspaceCustomMenuItemDto);
    return this.workspaceCustomMenuItemRepository.save(customMenuItem);
  }

  async findByWorkspaceId(workspaceId: string): Promise<WorkspaceCustomMenuItem[]> {
    return this.workspaceCustomMenuItemRepository.find({
      where: { workspaceId },
    });
  }
}
