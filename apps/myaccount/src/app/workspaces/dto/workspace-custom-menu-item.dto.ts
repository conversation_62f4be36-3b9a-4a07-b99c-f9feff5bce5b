import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class WorkspaceCustomMenuItemDto {
  @ApiProperty({
    type: String,
    description: 'Unique identifier of the custom menu item',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    type: String,
    description: 'Name of the custom menu item',
    example: 'Custom Dashboard',
  })
  @Expose()
  name: string;

  @ApiProperty({
    type: String,
    description: 'Icon for the custom menu item',
    example: 'dashboard-icon',
  })
  @Expose()
  icon: string;

  @ApiProperty({
    type: String,
    description: 'Workspace ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  workspaceId: string;

  @ApiProperty({
    type: Date,
    description: 'Creation date',
  })
  @Expose()
  createdDate: Date;

  @ApiProperty({
    type: Date,
    description: 'Last update date',
  })
  @Expose()
  updatedDate: Date;
}
