import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateWorkspaceCustomMenuItemRequestDto {
  @ApiProperty({
    type: String,
    description: 'Name of the custom menu item',
    example: 'Custom Dashboard',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: String,
    description: 'Icon for the custom menu item',
    example: 'dashboard-icon',
  })
  @IsString()
  @IsNotEmpty()
  icon: string;
}

export class CreateWorkspaceCustomMenuItemDto extends CreateWorkspaceCustomMenuItemRequestDto {
  @ApiProperty({
    type: String,
    description: 'Workspace ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  workspaceId: string;
}
