import { CryptoService } from '@keeps-node-apis/@core';
import { SmtpFacade } from './application/facades/smtp.facade';
import { GetSmtpConfigUseCase } from './application/use-cases/get-smtp-config.use-case';
import { UpdateSmtpConfigUseCase } from './application/use-cases/update-smtp-config.use-case';
import { TestSmtpConfigUseCase } from './application/use-cases/test-smtp-config.use-case';
import { UpdateEmailSendingStatusUseCase } from './application/use-cases/update-email-sending-status.use-case';
import { UpdateCustomSmtpUsageUseCase } from './application/use-cases/update-custom-smtp-usage.use-case';

export const PROVIDERS = [
  SmtpFacade,
  CryptoService,
  GetSmtpConfigUseCase,
  UpdateSmtpConfigUseCase,
  TestSmtpConfigUseCase,
  UpdateEmailSendingStatusUseCase,
  UpdateCustomSmtpUsageUseCase,
];

export const EXPORTS = [SmtpFacade];
