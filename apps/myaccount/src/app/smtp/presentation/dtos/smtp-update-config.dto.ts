import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

export class SmtpUpdateConfigDto {
  @ApiProperty({
    description: 'SMTP server host',
    example: 'smtp.gmail.com',
  })
  @IsString()
  @Expose()
  host: string;

  @ApiProperty({
    description: 'SMTP server port',
    example: 587,
  })
  @IsNumber()
  @Expose()
  port: number;

  @ApiProperty({
    description: 'SMTP authentication username',
    example: '<EMAIL>',
  })
  @IsString()
  @Expose()
  user: string;

  @ApiProperty({
    description: 'SMTP authentication password',
    example: 'your-password',
  })
  @IsString()
  password: string;

  @ApiProperty({
    description: 'Whether to use SSL/TLS',
    example: true,
  })
  @IsBoolean()
  @Expose()
  secure: boolean;

  @ApiProperty({
    description: 'Whether to reject unauthorized SSL/TLS certificates',
    example: true,
  })
  @IsBoolean()
  @Expose()
  rejectUnauthorized: boolean;

  @ApiProperty({
    description: 'Email address to send from',
    example: '<EMAIL>',
  })
  @IsString()
  @Expose()
  senderEmail: string;
}
