import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class SmtpConfigDto {
  @ApiProperty({
    description: 'Whether to use custom SMTP configuration',
    example: true,
  })
  @IsBoolean()
  @Expose()
  useOwnSmtp: boolean;

  @ApiProperty({
    description: 'Whether to enable email notifications',
    example: true,
  })
  @IsBoolean()
  @Expose()
  enableEmailNotifications: boolean;

  @ApiProperty({
    description: 'SMTP server host',
    example: 'smtp.gmail.com',
  })
  @IsString()
  @IsOptional()
  @Expose()
  host: string;

  @ApiProperty({
    description: 'SMTP server port',
    example: 587,
  })
  @IsNumber()
  @IsOptional()
  @Expose()
  port: number;

  @ApiProperty({
    description: 'SMTP authentication username',
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  @Expose()
  user: string;

  @ApiProperty({
    description: 'Whether to use SSL/TLS',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  @Expose()
  secure: boolean;

  @ApiProperty({
    description: 'Whether to reject unauthorized SSL/TLS certificates',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  @Expose()
  rejectUnauthorized: boolean;

  @ApiProperty({
    description: 'Email address to send from',
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  @Expose()
  senderEmail: string;
}
