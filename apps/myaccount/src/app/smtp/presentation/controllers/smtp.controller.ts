import { MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { Body, Controller, Get, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SmtpFacade } from '../../application/facades/smtp.facade';
import { SmtpConfigDto } from '../dtos/smtp-config.dto';
import { SmtpUpdateConfigDto } from '../dtos/smtp-update-config.dto';
import { UpdateEmailSendingStatusDto } from '../dtos/update-email-sending.dto';
import { UpdateSmtpCustomStatusDto } from '../dtos/update-smtp-custom-status.dto';

@Roles(MYACCOUNT_ADMIN_ROLES)
@ApiTags('SMTP Configuration')
@Controller('smtp')
export class SmtpController {
  constructor(private readonly smtpService: SmtpFacade) {}

  @Get('config')
  @ApiOperation({ summary: 'Retrieve SMTP configuration' })
  @ApiResponse({ status: HttpStatus.OK, type: SmtpConfigDto })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Configuration not found' })
  @Serialize(SmtpConfigDto)
  getSmtpConfig() {
    return this.smtpService.getSmtpConfig();
  }

  @Post('config')
  @ApiOperation({ summary: 'Create or Update SMTP configuration' })
  @ApiResponse({ status: HttpStatus.OK })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid configuration' })
  async updateSmtpConfig(@Body() smtpConfig: SmtpUpdateConfigDto) {
    await this.smtpService.updateSmtpConfig(smtpConfig);
  }

  @Post('config/test')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Test SMTP configuration' })
  @ApiResponse({ status: HttpStatus.OK })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid configuration' })
  async testSmtpWithConfig(@Body() smtpConfig: SmtpUpdateConfigDto) {
    await this.smtpService.testSmtpConfig(smtpConfig);
  }

  @Post('config/email-sending-status')
  @ApiOperation({ summary: 'Update email sending status' })
  @ApiResponse({ status: HttpStatus.OK })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid status' })
  async updateEmailSendingStatus(@Body() dto: UpdateEmailSendingStatusDto) {
    await this.smtpService.updateEmailSendingStatus(dto.enabled);
  }

  @Post('config/custom-smtp-status')
  @ApiOperation({ summary: 'Update custom SMTP usage status' })
  @ApiResponse({ status: HttpStatus.OK })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid status' })
  async updateCustomSmtpStatus(@Body() dto: UpdateSmtpCustomStatusDto) {
    await this.smtpService.updateCustomSmtpUsage(dto.useCustomSmtp);
  }
}
