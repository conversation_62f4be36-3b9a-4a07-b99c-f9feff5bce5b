import { Injectable } from '@nestjs/common';
import { SmtpConfigDto } from '../../presentation/dtos/smtp-config.dto';
import { SmtpConfigNotFoundException } from '../../domain/exceptions/smtp.domain.exceptions';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';

@Injectable()
export class GetSmtpConfigUseCase implements IUseCase<GetSmtpConfigInput, SmtpConfigDto> {
  constructor(private readonly workspacesService: WorkspacesService) {}

  async execute(input: GetSmtpConfigInput): Promise<SmtpConfigDto> {
    try {
      const workspace = await this.workspacesService.findOne(input.workspaceId);
      return new GetSmtpConfigOutput(workspace);
    } catch (error) {
      throw new SmtpConfigNotFoundException(input.workspaceId);
    }
  }
}

export class GetSmtpConfigInput {
  constructor(public readonly workspaceId: string) {}
}

export class GetSmtpConfigOutput {
  public readonly useOwnSmtp: boolean;
  public readonly enableEmailNotifications: boolean;
  public readonly host: string;
  public readonly port: number;
  public readonly user: string;
  public readonly password: string;
  public readonly secure: boolean;
  public readonly rejectUnauthorized: boolean;
  public readonly senderEmail: string;

  constructor(workspace: any) {
    this.useOwnSmtp = workspace.useOwnSmtp;
    this.enableEmailNotifications = workspace.enableEmailNotifications;
    this.host = workspace.smtpHost;
    this.port = workspace.smtpPort;
    this.user = workspace.smtpAuthUser;
    this.secure = workspace.smtpSecure;
    this.rejectUnauthorized = workspace.smtpRejectUnauthorized;
    this.senderEmail = workspace.smtpSenderEmail;
  }
}
