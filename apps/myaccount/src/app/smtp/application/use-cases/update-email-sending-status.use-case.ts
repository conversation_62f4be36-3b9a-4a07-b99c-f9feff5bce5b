import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpInvalidConfigException } from '../../domain/exceptions/smtp.domain.exceptions';

@Injectable()
export class UpdateEmailSendingStatusUseCase implements IUseCase<UpdateEmailSendingStatusInput, void> {
  constructor(private readonly workspacesService: WorkspacesService) {}

  async execute(input: UpdateEmailSendingStatusInput): Promise<void> {
    try {
      await this.workspacesService.update(input.workspaceId, {
        enableEmailNotifications: input.enabled,
      });
    } catch (error) {
      throw new SmtpInvalidConfigException(input.workspaceId);
    }
  }
}

export class UpdateEmailSendingStatusInput {
  constructor(
    public readonly workspaceId: string,
    public readonly enabled: boolean,
  ) {}
}
