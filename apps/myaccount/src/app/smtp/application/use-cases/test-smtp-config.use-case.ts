import { Injectable, Logger } from '@nestjs/common';
import { createTransport } from 'nodemailer';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { SmtpTestFailedException } from '../../domain/exceptions/smtp.domain.exceptions';
import { SmtpConfigValidator } from '../../domain/validators/smtp-config.validator';

@Injectable()
export class TestSmtpConfigUseCase implements IUseCase<TestSmtpConfigInput, void> {
  private readonly logger = new Logger(TestSmtpConfigUseCase.name);

  async execute(input: TestSmtpConfigInput): Promise<void> {
    SmtpConfigValidator.validate(input.smtpConfig);

    try {
      await this.testSmtpConnection(input.smtpConfig);
      this.logger.log(`SMTP configuration test successful for workspace ${input.workspaceId}`);
    } catch (error) {
      this.logger.error(`SMTP configuration test failed for workspace ${input.workspaceId}: ${error.message}`);
      throw new SmtpTestFailedException(input.workspaceId, error.message);
    }
  }

  private async testSmtpConnection(smtpConfig: TestSmtpConfigInput['smtpConfig']): Promise<void> {
    const transporter = createTransport({
      dnsTimeout: 10000,
      connectionTimeout: 10000,
      greetingTimeout: 10000,
      socketTimeout: 10000,
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      auth: {
        user: smtpConfig.user,
        pass: smtpConfig.password,
      },
      tls: {
        rejectUnauthorized: smtpConfig.rejectUnauthorized,
      },
    });

    await transporter.verify();
  }
}

export class TestSmtpConfigInput {
  constructor(
    public readonly workspaceId: string,
    public readonly smtpConfig: {
      host: string;
      port: number;
      user: string;
      password: string;
      secure: boolean;
      rejectUnauthorized: boolean;
      senderEmail: string;
    },
  ) {}
}
