import { Test, TestingModule } from '@nestjs/testing';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpInvalidConfigException } from '../../domain/exceptions/smtp.domain.exceptions';
import { UpdateCustomSmtpUsageUseCase } from './update-custom-smtp-usage.use-case';

describe('UpdateCustomSmtpUsageUseCase', () => {
  let useCase: UpdateCustomSmtpUsageUseCase;
  let workspacesService: WorkspacesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateCustomSmtpUsageUseCase,
        {
          provide: WorkspacesService,
          useValue: {
            update: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    useCase = module.get<UpdateCustomSmtpUsageUseCase>(UpdateCustomSmtpUsageUseCase);
    workspacesService = module.get<WorkspacesService>(WorkspacesService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should enable custom SMTP usage successfully', async () => {
      await useCase.execute({
        workspaceId: 'workspace-1',
        useCustomSmtp: true,
      });

      expect(workspacesService.update).toHaveBeenCalledWith('workspace-1', {
        useOwnSmtp: true,
      });
      expect(workspacesService.update).toHaveBeenCalledTimes(1);
    });

    it('should disable custom SMTP usage successfully', async () => {
      await useCase.execute({
        workspaceId: 'workspace-1',
        useCustomSmtp: false,
      });

      expect(workspacesService.update).toHaveBeenCalledWith('workspace-1', {
        useOwnSmtp: false,
      });
      expect(workspacesService.update).toHaveBeenCalledTimes(1);
    });

    it('should throw SmtpInvalidConfigException when workspace does not exist', async () => {
      jest.spyOn(workspacesService, 'update').mockRejectedValue(new Error());

      await expect(
        useCase.execute({
          workspaceId: 'non-existent',
          useCustomSmtp: true,
        }),
      ).rejects.toThrow(SmtpInvalidConfigException);

      expect(workspacesService.update).toHaveBeenCalledTimes(1);
    });
  });
});
