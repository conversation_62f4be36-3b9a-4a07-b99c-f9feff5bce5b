import { Test, TestingModule } from '@nestjs/testing';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpInvalidConfigException } from '../../domain/exceptions/smtp.domain.exceptions';
import { UpdateEmailSendingStatusUseCase } from './update-email-sending-status.use-case';

describe('UpdateEmailSendingStatusUseCase', () => {
  let useCase: UpdateEmailSendingStatusUseCase;
  let workspacesService: WorkspacesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateEmailSendingStatusUseCase,
        {
          provide: WorkspacesService,
          useValue: {
            update: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    useCase = module.get<UpdateEmailSendingStatusUseCase>(UpdateEmailSendingStatusUseCase);
    workspacesService = module.get<WorkspacesService>(WorkspacesService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should enable email notifications successfully', async () => {
      await useCase.execute({
        workspaceId: 'workspace-1',
        enabled: true,
      });

      expect(workspacesService.update).toHaveBeenCalledWith('workspace-1', {
        enableEmailNotifications: true,
      });
      expect(workspacesService.update).toHaveBeenCalledTimes(1);
    });

    it('should disable email notifications successfully', async () => {
      await useCase.execute({
        workspaceId: 'workspace-1',
        enabled: false,
      });

      expect(workspacesService.update).toHaveBeenCalledWith('workspace-1', {
        enableEmailNotifications: false,
      });
      expect(workspacesService.update).toHaveBeenCalledTimes(1);
    });

    it('should throw SmtpInvalidConfigException when workspace does not exist', async () => {
      jest.spyOn(workspacesService, 'update').mockRejectedValue(new Error());

      await expect(
        useCase.execute({
          workspaceId: 'non-existent',
          enabled: true,
        }),
      ).rejects.toThrow(SmtpInvalidConfigException);
    });
  });
});
