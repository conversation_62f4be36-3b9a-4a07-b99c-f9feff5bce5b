import { Test, TestingModule } from '@nestjs/testing';
import { GetSmtpConfigUseCase } from './get-smtp-config.use-case';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpConfigNotFoundException } from '../../domain/exceptions/smtp.domain.exceptions';
import { Workspace } from '../../../entities/workspace.entity';

describe('GetSmtpConfigUseCase', () => {
  let useCase: GetSmtpConfigUseCase;
  let workspacesService: WorkspacesService;

  const mockWorkspace = {
    useOwnSmtp: true,
    enableEmailNotifications: true,
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    smtpAuthUser: '<EMAIL>',
    smtpSecure: true,
    smtpRejectUnauthorized: false,
    smtpSenderEmail: '<EMAIL>',
  } as Partial<Workspace>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetSmtpConfigUseCase,
        {
          provide: WorkspacesService,
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get<GetSmtpConfigUseCase>(GetSmtpConfigUseCase);
    workspacesService = module.get<WorkspacesService>(WorkspacesService);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should return SMTP configuration when workspace exists', async () => {
      jest.spyOn(workspacesService, 'findOne').mockResolvedValue(mockWorkspace as Workspace);

      const result = await useCase.execute({ workspaceId: 'workspace-1' });

      expect(result).toEqual({
        useOwnSmtp: true,
        enableEmailNotifications: true,
        host: 'smtp.example.com',
        port: 587,
        user: '<EMAIL>',
        secure: true,
        rejectUnauthorized: false,
        senderEmail: '<EMAIL>',
      });
      expect(workspacesService.findOne).toHaveBeenCalledWith('workspace-1');
    });

    it('should throw SmtpConfigNotFoundException when workspace does not exist', async () => {
      jest.spyOn(workspacesService, 'findOne').mockRejectedValue(new Error());

      await expect(useCase.execute({ workspaceId: 'non-existent' })).rejects.toThrow(SmtpConfigNotFoundException);
    });
  });
});
