import { Test, TestingModule } from '@nestjs/testing';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpInvalidConfigException } from '../../domain/exceptions/smtp.domain.exceptions';
import { SmtpConfigValidator } from '../../domain/validators/smtp-config.validator';
import { UpdateSmtpConfigUseCase } from './update-smtp-config.use-case';

jest.mock('../../domain/validators/smtp-config.validator');

describe('UpdateSmtpConfigUseCase', () => {
  let useCase: UpdateSmtpConfigUseCase;
  let workspacesService: WorkspacesService;

  const mockSmtpConfig = {
    host: 'smtp.example.com',
    port: 587,
    user: '<EMAIL>',
    password: 'password123',
    secure: true,
    rejectUnauthorized: false,
    senderEmail: '<EMAIL>',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateSmtpConfigUseCase,
        {
          provide: WorkspacesService,
          useValue: {
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get<UpdateSmtpConfigUseCase>(UpdateSmtpConfigUseCase);
    workspacesService = module.get<WorkspacesService>(WorkspacesService);

    jest.clearAllMocks();
    (SmtpConfigValidator.validate as jest.Mock).mockImplementation(() => true);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    it('should update SMTP configuration successfully', async () => {
      jest.spyOn(workspacesService, 'update').mockResolvedValue(undefined);

      await useCase.execute({
        workspaceId: 'workspace-1',
        smtpConfig: mockSmtpConfig,
      });

      expect(SmtpConfigValidator.validate).toHaveBeenCalledWith(mockSmtpConfig);
      expect(workspacesService.update).toHaveBeenCalledWith('workspace-1', {
        smtpHost: mockSmtpConfig.host,
        smtpPort: mockSmtpConfig.port,
        smtpAuthUser: mockSmtpConfig.user,
        smtpAuthPass: 'password123',
        smtpSecure: mockSmtpConfig.secure,
        smtpRejectUnauthorized: mockSmtpConfig.rejectUnauthorized,
        smtpSenderEmail: mockSmtpConfig.senderEmail,
      });
    });

    it('should update SMTP configuration without password', async () => {
      jest.spyOn(workspacesService, 'update').mockResolvedValue(undefined);

      const configWithoutPassword = { ...mockSmtpConfig };
      delete configWithoutPassword.password;

      await useCase.execute({
        workspaceId: 'workspace-1',
        smtpConfig: configWithoutPassword,
      });

      expect(SmtpConfigValidator.validate).toHaveBeenCalledWith(configWithoutPassword);
      expect(workspacesService.update).toHaveBeenCalledWith('workspace-1', {
        smtpHost: mockSmtpConfig.host,
        smtpPort: mockSmtpConfig.port,
        smtpAuthUser: mockSmtpConfig.user,
        smtpAuthPass: null,
        smtpSecure: mockSmtpConfig.secure,
        smtpRejectUnauthorized: mockSmtpConfig.rejectUnauthorized,
        smtpSenderEmail: mockSmtpConfig.senderEmail,
      });
    });

    it('should throw SmtpInvalidConfigException when workspace does not exist', async () => {
      jest.spyOn(workspacesService, 'update').mockRejectedValue(new Error());

      await expect(
        useCase.execute({
          workspaceId: 'non-existent',
          smtpConfig: mockSmtpConfig,
        }),
      ).rejects.toThrow(SmtpInvalidConfigException);
    });

    it('should throw SmtpInvalidConfigException when config is invalid', async () => {
      (SmtpConfigValidator.validate as jest.Mock).mockImplementation(() => {
        throw new SmtpInvalidConfigException('Invalid SMTP configuration');
      });

      await expect(
        useCase.execute({
          workspaceId: 'workspace-1',
          smtpConfig: mockSmtpConfig,
        }),
      ).rejects.toThrow(SmtpInvalidConfigException);

      expect(SmtpConfigValidator.validate).toHaveBeenCalledWith(mockSmtpConfig);
      expect(workspacesService.update).not.toHaveBeenCalled();
    });
  });
});
