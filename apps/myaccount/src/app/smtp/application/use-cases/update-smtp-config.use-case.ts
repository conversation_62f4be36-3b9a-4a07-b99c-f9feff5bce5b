import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpInvalidConfigException } from '../../domain/exceptions/smtp.domain.exceptions';
import { SmtpConfigValidator } from '../../domain/validators/smtp-config.validator';

@Injectable()
export class UpdateSmtpConfigUseCase implements IUseCase<UpdateSmtpConfigInput, void> {
  constructor(private readonly workspacesService: WorkspacesService) {}

  async execute(input: UpdateSmtpConfigInput): Promise<void> {
    SmtpConfigValidator.validate(input.smtpConfig);

    try {
      await this.workspacesService.update(input.workspaceId, {
        smtpHost: input.smtpConfig.host,
        smtpPort: input.smtpConfig.port,
        smtpAuthUser: input.smtpConfig.user,
        smtpAuthPass: input.smtpConfig.password ? input.smtpConfig.password : null,
        smtpSecure: input.smtpConfig.secure,
        smtpRejectUnauthorized: input.smtpConfig.rejectUnauthorized,
        smtpSenderEmail: input.smtpConfig.senderEmail,
      });
    } catch (error) {
      throw new SmtpInvalidConfigException(input.workspaceId);
    }
  }
}

export class UpdateSmtpConfigInput {
  constructor(
    public readonly workspaceId: string,
    public readonly smtpConfig: {
      host: string;
      port: number;
      user: string;
      password: string;
      secure: boolean;
      rejectUnauthorized: boolean;
      senderEmail: string;
    },
  ) {}
}
