import { Injectable } from '@nestjs/common';
import { IUseCase } from '../../../@core/common/interfaces/use-cases/base.use-case.interface';
import { WorkspacesService } from '../../../workspaces/services/workspaces.service';
import { SmtpInvalidConfigException } from '../../domain/exceptions/smtp.domain.exceptions';

@Injectable()
export class UpdateCustomSmtpUsageUseCase implements IUseCase<UpdateCustomSmtpUsageInput, void> {
  constructor(private readonly workspacesService: WorkspacesService) {}

  async execute(input: UpdateCustomSmtpUsageInput): Promise<void> {
    try {
      await this.workspacesService.update(input.workspaceId, {
        useOwnSmtp: input.useCustomSmtp,
      });
    } catch (error) {
      throw new SmtpInvalidConfigException(input.workspaceId);
    }
  }
}

export class UpdateCustomSmtpUsageInput {
  constructor(
    public readonly workspaceId: string,
    public readonly useCustomSmtp: boolean,
  ) {}
}
