import { Injectable } from '@nestjs/common';
import { TenantService } from '@keeps-node-apis/@core';
import { SmtpConfigDto } from '../../presentation/dtos/smtp-config.dto';
import { SmtpUpdateConfigDto } from '../../presentation/dtos/smtp-update-config.dto';
import { GetSmtpConfigInput, GetSmtpConfigUseCase } from '../use-cases/get-smtp-config.use-case';
import { TestSmtpConfigInput, TestSmtpConfigUseCase } from '../use-cases/test-smtp-config.use-case';
import {
  UpdateCustomSmtpUsageInput,
  UpdateCustomSmtpUsageUseCase,
} from '../use-cases/update-custom-smtp-usage.use-case';
import {
  UpdateEmailSendingStatusInput,
  UpdateEmailSendingStatusUseCase,
} from '../use-cases/update-email-sending-status.use-case';
import { UpdateSmtpConfigInput, UpdateSmtpConfigUseCase } from '../use-cases/update-smtp-config.use-case';

@Injectable()
export class SmtpFacade {
  constructor(
    private readonly tenantService: TenantService,
    private readonly getSmtpConfigUseCase: GetSmtpConfigUseCase,
    private readonly updateSmtpConfigUseCase: UpdateSmtpConfigUseCase,
    private readonly testSmtpConfigUseCase: TestSmtpConfigUseCase,
    private readonly updateEmailSendingStatusUseCase: UpdateEmailSendingStatusUseCase,
    private readonly updateCustomSmtpUsageUseCase: UpdateCustomSmtpUsageUseCase,
  ) {}

  async getSmtpConfig(): Promise<SmtpConfigDto> {
    const tenantId = this.tenantService.getTenantId();
    return this.getSmtpConfigUseCase.execute(new GetSmtpConfigInput(tenantId));
  }

  async updateSmtpConfig(smtpConfigDto: SmtpUpdateConfigDto): Promise<void> {
    const tenantId = this.tenantService.getTenantId();
    await this.updateSmtpConfigUseCase.execute(new UpdateSmtpConfigInput(tenantId, smtpConfigDto));
  }

  async testSmtpConfig(smtpConfig: SmtpUpdateConfigDto): Promise<void> {
    const tenantId = this.tenantService.getTenantId();
    await this.testSmtpConfigUseCase.execute(new TestSmtpConfigInput(tenantId, smtpConfig));
  }

  async updateEmailSendingStatus(enabled: boolean): Promise<void> {
    const tenantId = this.tenantService.getTenantId();
    await this.updateEmailSendingStatusUseCase.execute(new UpdateEmailSendingStatusInput(tenantId, enabled));
  }

  async updateCustomSmtpUsage(useCustomSmtp: boolean): Promise<void> {
    const tenantId = this.tenantService.getTenantId();
    await this.updateCustomSmtpUsageUseCase.execute(new UpdateCustomSmtpUsageInput(tenantId, useCustomSmtp));
  }
}
