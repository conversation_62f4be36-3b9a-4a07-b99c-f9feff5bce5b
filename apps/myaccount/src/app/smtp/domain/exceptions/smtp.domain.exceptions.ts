import { DomainException } from '../../../@core/common/exceptions/base.exception';

export class SmtpDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `SMTP_${errorCode}`, details);
  }
}

export class SmtpConfigNotFoundException extends SmtpDomainException {
  constructor(workspaceId: string) {
    super(`SMTP configuration not found for workspace ${workspaceId}`, 'CONFIG_NOT_FOUND');
  }
}

export class SmtpTestFailedException extends SmtpDomainException {
  constructor(workspaceId: string, error: string) {
    super(`SMTP test failed for workspace ${workspaceId}: ${error}`, 'TEST_FAILED');
  }
}

export class SmtpInvalidConfigException extends SmtpDomainException {
  constructor(error: string) {
    super(`Invalid SMTP configuration: ${error}`, 'INVALID_CONFIG');
  }
}
