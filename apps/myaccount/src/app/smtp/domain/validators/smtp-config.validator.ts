import { SmtpInvalidConfigException } from '../exceptions/smtp.domain.exceptions';

export class SmtpConfigValidator {
  static validate(config: {
    host?: string;
    port?: number;
    user?: string;
    password?: string;
    secure?: boolean;
    rejectUnauthorized?: boolean;
    senderEmail?: string;
  }): void {
    if (!config.host) {
      throw new SmtpInvalidConfigException('Host is required when using custom SMTP');
    }
    if (!config.port) {
      throw new SmtpInvalidConfigException('Port is required when using custom SMTP');
    }
    if (!config.user) {
      throw new SmtpInvalidConfigException('User is required when using custom SMTP');
    }
    if (!config.password) {
      throw new SmtpInvalidConfigException('Password is required when using custom SMTP');
    }
    if (!config.senderEmail) {
      throw new SmtpInvalidConfigException('Sender email is required when using custom SMTP');
    }
  }
}
