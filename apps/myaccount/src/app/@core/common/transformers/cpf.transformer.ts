import { cpf } from 'cpf-cnpj-validator';
import { InvalidCPFException } from '../exceptions/invalid-cpf.exception';
import { TransformFnParams } from 'class-transformer';

export function TransformCpf({ value }: TransformFnParams): string {
  if (value === null || value === '') {
    return null;
  }
  const cleaned = cpf.strip(value);

  if (!cpf.isValid(cleaned)) {
    throw new InvalidCPFException(`${value} is invalid`);
  }

  value = cleaned;
  return value.toString();
}
