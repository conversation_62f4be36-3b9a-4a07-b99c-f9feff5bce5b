import { Injectable } from '@nestjs/common';
import { DataSource, ILike, Repository } from 'typeorm';
import { JobFunction } from '../../entities/job-function.entity';
import { JobFunctionsRepository } from '../interfaces/job-functions.repository';
import { JobFunctionCreateDto } from '../presentation/dtos/job-function-create.dto';

@Injectable()
export class JobFunctionsTypeOrmRepository implements JobFunctionsRepository {
  private readonly repository: Repository<JobFunction>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(JobFunction);
  }

  async list(workspaceId: string, searchName?: string): Promise<JobFunction[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('job_function')
      .where('job_function.workspaceId = :workspaceId', {
        workspaceId,
      });

    if (searchName) {
      queryBuilder.andWhere('job_function.name ILIKE :searchName', { searchName: `%${searchName}%` });
    }

    return queryBuilder.getMany();
  }

  async findOrCreate(name: string, workspaceId: string): Promise<JobFunction> {
    let job = await this.repository.findOne({ where: { name, workspaceId } });
    if (!job) {
      job = this.repository.create({ name, workspaceId });
      await this.repository.save(job);
    }
    return job;
  }

  create(workspaceId: string, createDto: JobFunctionCreateDto): Promise<JobFunction> {
    const jobFunction = this.repository.create({ name: createDto.name, workspaceId: workspaceId });
    return this.repository.save(jobFunction);
  }

  async update(workspaceId: string, jobFunctionId: string, updateDto: JobFunctionCreateDto): Promise<JobFunction> {
    const jobFunction = await this.repository.findOneByOrFail({ id: jobFunctionId, workspaceId: workspaceId });
    const updated = { ...jobFunction, name: updateDto.name };
    return this.repository.save(updated);
  }

  async delete(workspaceId: string, jobFunctionId: string): Promise<void> {
    await this.repository.delete({ id: jobFunctionId, workspaceId });
  }

  async findOneByName(workspaceId: string, name: string): Promise<JobFunction> {
    return this.repository.findOneBy({ workspaceId, name: ILike(name) });
  }
}
