import { Injectable } from '@nestjs/common';
import { DataSource, ILike, Repository } from 'typeorm';
import { Job } from '../../entities/job.entity';
import { JobsRepository } from '../interfaces/jobs.repository';
import { JobCreateDto } from '../presentation/dtos/job-create.dto';

@Injectable()
export class JobsTypeOrmRepository implements JobsRepository {
  private readonly repository: Repository<Job>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(Job);
  }

  list(workspaceId: string, searchName?: string): Promise<Job[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('job')
      .where('job.workspaceId = :workspaceId', { workspaceId });

    if (searchName) {
      queryBuilder.andWhere('job.name ILIKE :searchName', { searchName: `%${searchName}%` });
    }

    return queryBuilder.getMany();
  }

  async findOrCreate(name: string, workspaceId: string): Promise<Job> {
    let job = await this.repository.findOne({ where: { name, workspaceId } });
    if (!job) {
      job = this.repository.create({ name, workspaceId });
      await this.repository.save(job);
    }
    return job;
  }

  create(workspaceId: string, createDto: JobCreateDto): Promise<Job> {
    const job = this.repository.create({ name: createDto.name, workspaceId });
    return this.repository.save(job);
  }

  async update(workspaceId: string, jobId: string, updateDto: JobCreateDto): Promise<Job> {
    const job = await this.repository.findOneBy({ id: jobId, workspaceId: workspaceId });
    const updated = { ...job, name: updateDto.name };
    return this.repository.save(updated);
  }

  async delete(workspaceId: string, jobId: string) {
    await this.repository.delete({ id: jobId, workspaceId });
  }

  async findOneByName(workspaceId: string, name: string): Promise<Job> {
    return this.repository.findOneBy({ workspaceId, name: ILike(name) });
  }
}
