import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { JobsService } from '../../services/jobs.service';
import { JobDto } from '../dtos/job.dto';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { ApiNoContentResponse, ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JobCreateDto } from '../dtos/job-create.dto';

/**
 * Controller responsible for handling job-related API endpoints.
 * Restricted to users with that company and Konquest Admins
 */
@ApiTags('Jobs')
@Controller('jobs')
@Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES])
export class JobsController {
  constructor(private readonly jobsService: JobsService) {}

  /**
   * Retrieves a list of allowed jobs for a given workspace.
   * @param searchName Optional query parameter to filter jobs by name
   * @returns A list of jobs in the JobResponseDto format
   * @throws {HttpStatus.FORBIDDEN} When the user lacks sufficient permissions
   */
  @Get()
  @Serialize(JobDto)
  @ApiOperation({ summary: 'Retrieve all allowed jobs for a workspace' })
  @ApiOkResponse({ description: 'Jobs retrieved successfully', type: JobDto, isArray: true })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  async list(@Query('searchName') searchName?: string): Promise<JobDto[]> {
    return await this.jobsService.list(searchName);
  }

  /**
   * Create a new job
   * @param createDto The job creation dto
   * @returns The created job
   * @throws JobNameAlreadyExistsDomainException When the provided name already exists and belongs to another job
   * @throws {HttpStatus.FORBIDDEN} When the user lacks sufficient permissions
   */
  @Post()
  @Serialize(JobDto)
  @ApiOperation({ summary: 'Create a new job for a workspace' })
  @ApiOkResponse({ description: 'Job created successfully', type: JobDto })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  async create(@Body() createDto: JobCreateDto): Promise<JobDto> {
    return await this.jobsService.create(createDto);
  }

  /**
   * Update an existing job
   * @param id The id of the job to update
   * @param updateDto The update dto
   * @returns The updated job
   * @throws JobNameAlreadyExistsDomainException When the provided name already exists and belongs to another job
   * @throws HttpStatus.FORBIDDEN When the user lacks sufficient permissions
   */
  @Patch('/:id')
  @Serialize(JobDto)
  @ApiOperation({ summary: 'Update an existing job for in a workspace' })
  @ApiOkResponse({ description: 'Job updated successfully', type: JobDto })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  async update(@Param('id') id: string, @Body() updateDto: JobCreateDto): Promise<JobDto> {
    return this.jobsService.update(id, updateDto);
  }

  /**
   * Delete an existing job
   * @param id The id of the job function to delete
   * @throws HttpStatus.FORBIDDEN When the user lacks sufficient permissions
   */
  @Delete('/:id')
  @ApiOperation({ summary: 'Delete a job in a workspace' })
  @ApiNoContentResponse({ description: 'Job deleted successfully' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  async delete(@Param('id') id: string): Promise<void> {
    return this.jobsService.delete(id);
  }
}
