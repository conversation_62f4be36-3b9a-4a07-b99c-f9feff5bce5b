import { Injectable, Logger } from '@nestjs/common';
import { JobFunctionsRepository } from '../interfaces/job-functions.repository';
import { TenantService } from '@keeps-node-apis/@core';
import { JobFunctionCreateDto } from '../presentation/dtos/job-function-create.dto';
import { JobFunctionNameAlreadyExistsDomainException } from '../domain/exceptions/job-function.domain.exceptions';

/**
 * Service for managing job function-related operations.
 */
@Injectable()
export class JobFunctionsService {
  private readonly logger = new Logger(JobFunctionsService.name);

  constructor(
    private readonly jobFunctionRepository: JobFunctionsRepository,
    private readonly tenantService: TenantService,
  ) {}

  /**
   * Retrieves a list of allowed job functions for a given workspace.
   * @param searchName Optional parameter to filter job functions by name
   * @returns A promise resolving to an array of JobResponseDto objects
   * @throws {Error} When the repository fails to fetch job functions
   */
  list(searchName?: string) {
    const tenantId = this.tenantService.getTenantId();
    return this.jobFunctionRepository.list(tenantId, searchName);
  }

  async create(createDto: JobFunctionCreateDto) {
    const tenantId = this.tenantService.getTenantId();
    const existing = await this.jobFunctionRepository.findOneByName(tenantId, createDto.name);
    if (existing) {
      this.logger.warn(`Attempt to create a job function with duplicate name: ${createDto.name}`);
      throw new JobFunctionNameAlreadyExistsDomainException(existing.name);
    }
    return this.jobFunctionRepository.create(tenantId, createDto);
  }

  async update(jobFunctionId: string, updateDto: JobFunctionCreateDto) {
    const tenantId = this.tenantService.getTenantId();

    const existing = await this.jobFunctionRepository.findOneByName(tenantId, updateDto.name);
    if (existing && existing.id !== jobFunctionId) {
      this.logger.warn(`Attempt to update a job function with duplicate name: ${updateDto.name}`);
      throw new JobFunctionNameAlreadyExistsDomainException(existing.name);
    }
    return this.jobFunctionRepository.update(tenantId, jobFunctionId, updateDto);
  }

  delete(jobFunctionId: string) {
    const tenantId = this.tenantService.getTenantId();
    return this.jobFunctionRepository.delete(tenantId, jobFunctionId);
  }
}
