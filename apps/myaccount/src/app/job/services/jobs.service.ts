import { Injectable, Logger } from '@nestjs/common';
import { TenantService } from '@keeps-node-apis/@core';
import { JobsRepository } from '../interfaces/jobs.repository';
import { JobCreateDto } from '../presentation/dtos/job-create.dto';
import { JobNameAlreadyExistsDomainException } from '../domain/exceptions/job.domain.exceptions';

@Injectable()
export class JobsService {
  private readonly logger = new Logger(JobsService.name);

  constructor(
    private readonly jobsRepository: JobsRepository,
    private readonly tenantService: TenantService,
  ) {}

  list(searchName?: string) {
    const tenantId = this.tenantService.getTenantId();
    return this.jobsRepository.list(tenantId, searchName);
  }

  async create(createDto: JobCreateDto) {
    const tenantId = this.tenantService.getTenantId();

    const existing = await this.jobsRepository.findOneByName(tenantId, createDto.name);
    if (existing) {
      this.logger.warn(`Attempt to create a job with duplicate name: ${createDto.name}`);
      throw new JobNameAlreadyExistsDomainException(existing.name);
    }

    return this.jobsRepository.create(tenantId, createDto);
  }

  async update(jobId: string, updateDto: JobCreateDto) {
    const tenantId = this.tenantService.getTenantId();

    const existing = await this.jobsRepository.findOneByName(tenantId, updateDto.name);
    if (existing && existing.id !== jobId) {
      this.logger.warn(`Attempt to update a job with duplicate name: ${updateDto.name}`);
      throw new JobNameAlreadyExistsDomainException(existing.name);
    }

    return this.jobsRepository.update(tenantId, jobId, updateDto);
  }

  delete(jobId: string) {
    const tenantId = this.tenantService.getTenantId();
    return this.jobsRepository.delete(tenantId, jobId);
  }
}
