import { KpCacheModule } from '@keeps-node-apis/@core';
import { Modu<PERSON> } from '@nestjs/common';
import JobProviders from './job.providers';
import { JobsController } from './presentation/controllers/job.controller';
import { EntitiesModule } from '../entities/entities.module';
import { JobFunctionsRepository } from './interfaces/job-functions.repository';
import { JobsRepository } from './interfaces/jobs.repository';
import { JobFunctionsController } from './presentation/controllers/job-functions.controller';

@Module({
  imports: [KpCacheModule, EntitiesModule],
  controllers: [JobsController, JobFunctionsController],
  providers: JobProviders,
  exports: [JobsRepository, JobFunctionsRepository],
})
export class JobModule {}
