import { Job } from '../../entities/job.entity';
import { JobCreateDto } from '../presentation/dtos/job-create.dto';

export abstract class JobsRepository {
  abstract list(workspaceId: string, searchName?: string): Promise<Job[]>;

  abstract findOrCreate(name: string, workspaceId: string): Promise<Job>;

  abstract create(workspaceId: string, createDto: JobCreateDto): Promise<Job>;

  abstract update(workspaceId: string, jobId: string, updateDto: JobCreateDto): Promise<Job>;

  abstract delete(workspaceId: string, jobId: string): Promise<void>;

  abstract findOneByName(workspaceId: string, name: string): Promise<Job>;
}
