import { JobFunction } from '../../entities/job-function.entity';
import { JobFunctionCreateDto } from '../presentation/dtos/job-function-create.dto';

export abstract class JobFunctionsRepository {
  abstract list(workspaceId: string, searchName?: string): Promise<JobFunction[]>;

  abstract findOrCreate(name: string, workspaceId: string): Promise<JobFunction>;

  abstract create(workspaceId: string, createDto: JobFunctionCreateDto): Promise<JobFunction>;

  abstract update(workspaceId: string, jobFunctionId: string, updateDto: JobFunctionCreateDto): Promise<JobFunction>;

  abstract delete(workspaceId: string, jobFunctionId: string): Promise<void>;

  abstract findOneByName(workspaceId: string, name: string): Promise<JobFunction>;
}
