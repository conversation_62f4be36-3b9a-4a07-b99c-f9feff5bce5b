import { Provider } from '@nestjs/common';
import { JobsService } from './services/jobs.service';
import { JobFunctionsTypeOrmRepository } from './repositories/job-function-type-orm.repository';

import { JobFunctionsService } from './services/job-function.service';
import { JobFunctionsRepository } from './interfaces/job-functions.repository';
import { JobsRepository } from './interfaces/jobs.repository';
import { JobsTypeOrmRepository } from './repositories/jobs-type-orm.repository';

const REPOSITORIES: Provider[] = [
  { provide: JobsRepository, useClass: JobsTypeOrmRepository },
  {
    provide: JobFunctionsRepository,
    useClass: JobFunctionsTypeOrmRepository,
  },
];

export default [JobsService, JobFunctionsService, ...REPOSITORIES];
