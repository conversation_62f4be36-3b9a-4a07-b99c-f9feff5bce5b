import { snakeToCamelCase } from '@keeps-node-apis/@core';
import { type ExecutionContext, Injectable, type NestInterceptor, type CallHandler, Optional } from '@nestjs/common';
import { type Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/** because the regenerated value's field is differ from original,
 * it is hard to declare return type.
 * the input type is also not meaningful.
 *
 * in: request layer (default: snakeToCamel),
 * out: response layer (default: camelToSnake).
 *
 * i.e. const DEFAULT_STRATEGY: Strategy = { in: snakeToCamel, out: camelToSnake };
 */
export class Strategy {
  in: (value: unknown) => unknown;
  out: (value: unknown) => unknown;
}
export const DEFAULT_STRATEGY: Strategy = {
  in: snakeToCamel,
  out: camelToSnake,
};

// where NestInterceptor<T, R>, T is stream of response, R is stream of value
@Injectable()
export class CaseSerializeInterceptor implements NestInterceptor<unknown, unknown> {
  constructor(@Optional() readonly strategy: Strategy = DEFAULT_STRATEGY) {}

  intercept(context: ExecutionContext, next: CallHandler<unknown>): Observable<unknown> {
    const request = context.switchToHttp().getRequest();
    request.body = this.strategy.in(request.body);
    request.query = this.strategy.in(request.query);

    return next.handle().pipe(map(this.strategy.out));
  }
}

export function camelToSnake<T>(value: T) {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(camelToSnake);
  }

  if (typeof value === 'object' && !(value instanceof Date)) {
    return Object.fromEntries(
      Object.entries(value).map(([key, value]) => [
        key
          .split(/(?=[A-Z])/)
          .join('_')
          .toLowerCase(),
        camelToSnake(value),
      ]),
    );
  }
  return value;
}

export function snakeToCamel<T>(value: T) {
  if (value === null || value === undefined) return value;

  if (Array.isArray(value)) return value.map(snakeToCamel);

  if (typeof value === 'object' && !(value instanceof Date)) {
    return Object.fromEntries(
      Object.entries(value).map(([key, value]) => [snakeToCamelCase(key), snakeToCamel(value)]),
    );
  }
  return value;
}
