import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BillingService } from './billing.service';
import { GenerateBillingDto } from './billing.dto';

@ApiTags('Billing')
@Controller('billing-general')
export class BillingController {
  constructor(private readonly billingService: BillingService) {}

  @Get()
  @ApiOperation({ summary: 'Generate billing records' })
  @ApiResponse({
    status: 200,
    description: 'Billing records generated successfully',
  })
  async generateBilling(@Query() query: GenerateBillingDto) {
    return this.billingService.generateBilling(query.startDate, query.endDate);
  }
}
