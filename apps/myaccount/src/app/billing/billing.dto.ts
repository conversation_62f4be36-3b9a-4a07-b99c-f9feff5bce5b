import { IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GenerateBillingDto {
  @ApiProperty({
    description: 'Start date for billing period',
    required: false,
    example: '2025-01-01',
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for billing period',
    required: false,
    example: '2025-01-31',
  })
  @IsOptional()
  @IsString()
  endDate?: string;
}
