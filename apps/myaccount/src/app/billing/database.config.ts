import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const smartzapConfig = registerAs('smartzapDatabase', (): TypeOrmModuleOptions => {
  return {
    name: 'smartzap',
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: +process.env.DB_PORT || 5432,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.SMARTZAP_DB_NAME,
    synchronize: false,
    logging: process.env.SMARTZAP_DB_DEBUG === 'true',
    extra: {
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 5000,
    },
  };
});

export const konquestConfig = registerAs('konquestDatabase', (): TypeOrmModuleOptions => {
  return {
    name: 'konquest',
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: +process.env.DB_PORT || 5432,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.KONQUEST_DB_NAME,
    synchronize: false,
    logging: process.env.KONQUEST_DB_DEBUG === 'true',
    extra: {
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 5000,
    },
  };
});
