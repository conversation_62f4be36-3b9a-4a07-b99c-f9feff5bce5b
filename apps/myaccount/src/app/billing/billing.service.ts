import { Injectable } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Company } from '../entities/company.entity';
import { DataSource, Repository } from 'typeorm';
import { CompanyBillingPlan } from '../entities/company-billing-plan.entity';
import { Billing } from '../entities/billing.entity';
import { Workspace } from '../entities/workspace.entity';
import { ServiceWorkspace } from '../entities/service-workspace.entity';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class BillingService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(CompanyBillingPlan)
    private readonly companyBillingPlanRepository: Repository<CompanyBillingPlan>,
    @InjectRepository(Billing)
    private readonly billingRepository: Repository<Billing>,
    @InjectRepository(Workspace)
    private readonly workspaceRepository: Repository<Workspace>,
    @InjectRepository(ServiceWorkspace)
    private readonly serviceWorkspaceRepository: Repository<ServiceWorkspace>,
    @InjectDataSource('smartzap') private readonly smartzapDataSource: DataSource,
    @InjectDataSource('konquest') private readonly konquestDataSource: DataSource,
  ) {}

  private readonly KONQUEST_ID = process.env.KONQUEST_ID;
  private readonly SMARTZAP_ID = process.env.SMARTZAP_ID;

  async generateBilling(startDate?: string, endDate?: string) {
    await this.generateApplicationBilling(this.KONQUEST_ID, startDate, endDate);
    await this.generateApplicationBilling(this.SMARTZAP_ID, startDate, endDate);
    return { status: 'completed' };
  }

  private async generateApplicationBilling(applicationId: string, startDate?: string, endDate?: string) {
    const companies = await this.getCompaniesWithApplication(applicationId);
    const { start, end } = this.formatDateRange(startDate, endDate);
    for (const company of companies) {
      await this.processCompanyBilling(company, applicationId, start, end);
    }
  }

  private async processCompanyBilling(company: Company, applicationId: string, startDate: Date, endDate: Date) {
    const workspaces = await this.getCompanyWorkspaces(company.id, applicationId);
    const used = await this.calculateUsage(applicationId, workspaces, startDate, endDate);
    const currentPlan = await this.getCurrentPlan(company.id, applicationId);

    const balance =
      applicationId === this.SMARTZAP_ID
        ? await this.calculateSmartzapBalance(company.id, currentPlan, used)
        : currentPlan - used;

    const billingId = uuidv4();

    const billing = this.billingRepository.create({
      id: billingId,
      company,
      application: { id: applicationId },
      monthlyPlan: currentPlan,
      used,
      balance,
      startDate,
      endDate,
      createdDate: new Date(),
    });

    await this.billingRepository.save(billing);
  }

  private async calculateUsage(
    applicationId: string,
    workspaces: Workspace[],
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    const workspaceIds = workspaces.map((w) => w.id);

    if (applicationId === this.KONQUEST_ID) {
      return this.calculateKonquestUsage(workspaceIds, startDate, endDate);
    }
    if (applicationId === this.SMARTZAP_ID) {
      return this.calculateSmartzapUsage(workspaceIds, startDate, endDate);
    }
    return 0;
  }

  private async calculateKonquestUsage(workspaceIds: string[], startDate: Date, endDate: Date): Promise<number> {
    if (workspaceIds.length === 0) {
      return 0;
    }

    const queryMissions = `
    SELECT DISTINCT(user_id)
    FROM learn_content_activity
    JOIN mission_stage_content ON learn_content_activity.mission_stage_content_id = mission_stage_content.id
    JOIN mission_stage ON mission_stage.id = mission_stage_content.stage_id
    JOIN mission_workspace ON mission_workspace.mission_id = mission_stage.mission_id
    WHERE mission_workspace.workspace_id IN (${workspaceIds.map((id) => `'${id}'`).join(',')})
    AND learn_content_activity.created_date >= '${startDate.toISOString()}'
    AND learn_content_activity.created_date <= '${endDate.toISOString()}';
  `;

    const queryPulses = `
    SELECT DISTINCT(user_id)
    FROM learn_content_activity
    JOIN pulse ON pulse.id = learn_content_activity.pulse_id
    JOIN pulse_channel ON pulse_channel.pulse_id = pulse.id
    JOIN channel ON pulse_channel.channel_id = channel.id
    WHERE channel.workspace_id IN (${workspaceIds.map((id) => `'${id}'`).join(',')})
    AND learn_content_activity.created_date >= '${startDate.toISOString()}'
    AND learn_content_activity.created_date <= '${endDate.toISOString()}';
  `;

    const missionsUsers = await this.konquestDataSource.query(queryMissions);
    const pulsesUsers = await this.konquestDataSource.query(queryPulses);
    const totalUsers = new Set([...missionsUsers, ...pulsesUsers].map((row) => row.user_id));
    return totalUsers.size;
  }

  private async calculateSmartzapUsage(workspaceIds: string[], startDate: Date, endDate: Date): Promise<number> {
    const query = `
      SELECT count(*)
      FROM public.schedule
      WHERE schedule.workspace_id IN (${workspaceIds.map((id) => `'${id}'`).join(',')})
      AND schedule.send_date >= '${startDate.toISOString()}'
      AND schedule.send_date <= '${endDate.toISOString()}'
      AND schedule.status != 'ERROR';
    `;

    const result = await this.smartzapDataSource.query(query);
    return parseInt(result[0].count, 10);
  }

  /**
   * Gets all companies that have active services for a specific application.
   * Only considers service workspaces with status = true (not soft-deleted).
   *
   * @param {string} applicationId - The ID of the application.
   * @returns {Promise<Company[]>} Array of companies with the application.
   */
  private async getCompaniesWithApplication(applicationId: string): Promise<Company[]> {
    const companyIds = await this.serviceWorkspaceRepository
      .createQueryBuilder('sw')
      .innerJoin('sw.service', 'service')
      .innerJoin('sw.workspace', 'workspace')
      .where('service.application_id = :applicationId', { applicationId })
      .andWhere('sw.status = :status', { status: true })
      .select('workspace.company_id', 'companyId')
      .distinct(true)
      .getRawMany();

    const ids = companyIds.map((row) => row.companyId);

    if (ids.length === 0) {
      return [];
    }

    return this.companyRepository.createQueryBuilder('company').where('company.id IN (:...ids)', { ids }).getMany();
  }

  private async getCompanyWorkspaces(companyId: string, applicationId: string): Promise<Workspace[]> {
    return this.workspaceRepository
      .createQueryBuilder('w')
      .innerJoin(
        ServiceWorkspace,
        'sw',
        'sw.workspace_id = w.id AND sw.service_id = :applicationId AND sw.status = :status',
        {
          applicationId,
          status: true,
        },
      )
      .where('w.company_id = :companyId', { companyId })
      .getMany();
  }

  private async getCurrentPlan(companyId: string, applicationId: string): Promise<number> {
    const plan = await this.companyBillingPlanRepository.findOne({
      where: {
        company: { id: companyId },
        application: { id: applicationId },
      },
    });

    return plan?.currentPlan || 0;
  }

  private async calculateSmartzapBalance(companyId: string, currentPlan: number, used: number): Promise<number> {
    const pastBillings = await this.billingRepository.find({
      where: {
        company: { id: companyId },
        application: { id: this.SMARTZAP_ID },
      },
      order: { endDate: 'DESC' },
      take: 2,
    });

    const cumulativeBalance = pastBillings.reduce((sum, billing) => sum + billing.balance, 0);
    return cumulativeBalance + (currentPlan - used);
  }

  private formatDateRange(start?: string, end?: string): { start: Date; end: Date } {
    if (start && end) {
      return {
        start: new Date(start),
        end: new Date(end),
      };
    }

    const date = new Date();
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    return { start: firstDay, end: lastDay };
  }
}
