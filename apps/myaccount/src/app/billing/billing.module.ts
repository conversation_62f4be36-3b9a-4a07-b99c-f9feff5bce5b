import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { EntitiesModule } from '../entities/entities.module';
import { BillingController } from './billing.controller';
import { BillingService } from './billing.service';
import { konquestConfig, smartzapConfig } from './database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [smartzapConfig, konquestConfig],
    }),
    TypeOrmModule.forRootAsync({
      name: 'smartzap',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => configService.get<TypeOrmModuleOptions>('smartzapDatabase'),
      inject: [ConfigService],
    }),
    TypeOrmModule.forRootAsync({
      name: 'konquest',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => configService.get<TypeOrmModuleOptions>('konquestDatabase'),
      inject: [ConfigService],
    }),
    EntitiesModule,
  ],
  controllers: [BillingController],
  providers: [BillingService],
  exports: [BillingService],
})
export class BillingModule {}
