import { MigrationInterface, QueryRunner } from "typeorm";

export class INITIAL1711759416516 implements MigrationInterface {
    name = 'INITIAL1711759416516'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "company_billing_plan" ("id" uuid NOT NULL, "current_plan" integer NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_date" TIMESTAMP WITH TIME ZONE NOT NULL, "application_id" uuid NOT NULL, "company_id" uuid NOT NULL, CONSTRAINT "UQ_a552046194408ac12d81226901a" UNIQUE ("application_id"), CONSTRAINT "UQ_96dbeadca50b60ed7f90fdb03dd" UNIQUE ("company_id"), CONSTRAINT "PK_338185418cbdd0d48288a98c598" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "company_billing_plan_pkey" ON "company_billing_plan" ("id") `);
        await queryRunner.query(`CREATE INDEX "company_billing_plan_company_id_7dfaa71e" ON "company_billing_plan" ("company_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "company_billing_plan_application_id_company_id_8f9d816f_uniq" ON "company_billing_plan" ("application_id", "company_id") `);
        await queryRunner.query(`CREATE INDEX "company_billing_plan_application_id_deed275d" ON "company_billing_plan" ("application_id") `);
        await queryRunner.query(`CREATE TABLE "service_workspace" ("id" uuid NOT NULL, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "workspace_id" uuid NOT NULL, "service_id" uuid NOT NULL, "custom_url" character varying(500), CONSTRAINT "UQ_a2240b36f0ef7c2712d53c8bfaa" UNIQUE ("workspace_id"), CONSTRAINT "UQ_298a63041c3b28c9635e52101ce" UNIQUE ("service_id"), CONSTRAINT "PK_2db6d90a4b1108ad8d44780c474" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "service_company_company_id_2a36f3aa" ON "service_workspace" ("workspace_id") `);
        await queryRunner.query(`CREATE INDEX "service_company_service_id_0bb52aab" ON "service_workspace" ("service_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "service_company_service_id_company_id_242d1e51_uniq" ON "service_workspace" ("service_id", "workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "service_company_pkey" ON "service_workspace" ("id") `);
        await queryRunner.query(`CREATE TABLE "service" ("id" uuid NOT NULL, "name" character varying(200) NOT NULL, "description" text, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "application_id" uuid NOT NULL, CONSTRAINT "PK_85a21558c006647cd76fdce044b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "service_pkey" ON "service" ("id") `);
        await queryRunner.query(`CREATE INDEX "service_application_id_a9f6d464" ON "service" ("application_id") `);
        await queryRunner.query(`CREATE TABLE "gamification_ranking_workspace" ("id" uuid NOT NULL, "ranking" character varying(30) NOT NULL, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "service_id" uuid NOT NULL, "workspace_id" uuid NOT NULL, CONSTRAINT "UQ_13a7305ffd8f8a07ad4cfb11c90" UNIQUE ("ranking"), CONSTRAINT "UQ_2885f47a14493d6cdac429602e8" UNIQUE ("service_id"), CONSTRAINT "UQ_15061d30b914cfc189593a96e8b" UNIQUE ("workspace_id"), CONSTRAINT "PK_7bf596998b02c60d539de9ade0f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "gamification_ranking_workspace_workspace_id_542f21d1" ON "gamification_ranking_workspace" ("workspace_id") `);
        await queryRunner.query(`CREATE INDEX "gamification_ranking_workspace_service_id_bc622e08" ON "gamification_ranking_workspace" ("service_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "gamification_ranking_wor_service_id_workspace_id__5e74ea63_uniq" ON "gamification_ranking_workspace" ("ranking", "service_id", "workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "gamification_ranking_workspace_pkey" ON "gamification_ranking_workspace" ("id") `);
        await queryRunner.query(`CREATE TABLE "idp_workspace" ("id" uuid NOT NULL, "idp" character varying(200) NOT NULL, "workspace_id" uuid NOT NULL, CONSTRAINT "UQ_398bd9ed448134989a00ca83160" UNIQUE ("idp"), CONSTRAINT "UQ_3c3520a41d1caa49e78eb65873c" UNIQUE ("workspace_id"), CONSTRAINT "PK_e74307f62f12dedc96dbd0dc592" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "idp_workspace_workspace_id_599314dd" ON "idp_workspace" ("workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "idp_workspace_idp_workspace_id_afc5c21e_uniq" ON "idp_workspace" ("idp", "workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "idp_workspace_pkey" ON "idp_workspace" ("id") `);
        await queryRunner.query(`CREATE TABLE "job_function" ("id" uuid NOT NULL, "name" character varying(200), "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "workspace_id" uuid NOT NULL, CONSTRAINT "UQ_56bcb31b17e3f593c7a3b36e0a1" UNIQUE ("name"), CONSTRAINT "UQ_0b2853227c9769b84f7043a8776" UNIQUE ("workspace_id"), CONSTRAINT "PK_fedb95d3a7212beeb715a287743" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "job_function_workspace_id_e0b8a9d7" ON "job_function" ("workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "job_function_name_workspace_id_4c767397_uniq" ON "job_function" ("name", "workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "job_function_pkey" ON "job_function" ("id") `);
        await queryRunner.query(`CREATE TABLE "language_preference" ("id" uuid NOT NULL, "name" character varying(200) NOT NULL, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_377f4fb42ab604918e86aa4ec68" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "language_preference_pkey" ON "language_preference" ("id") `);
        await queryRunner.query(`CREATE TABLE "role" ("id" uuid NOT NULL, "key" character varying(200) NOT NULL, "description" text, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "application_id" uuid NOT NULL, "name" character varying(200), CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "profile_application_pkey" ON "role" ("id") `);
        await queryRunner.query(`CREATE INDEX "profile_application_application_id_27f10135" ON "role" ("application_id") `);
        await queryRunner.query(`CREATE TABLE "user_role_workspace" ("id" uuid NOT NULL, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "workspace_id" uuid NOT NULL, "role_id" uuid NOT NULL, "user_id" uuid NOT NULL, "self_sign_up" boolean NOT NULL, CONSTRAINT "UQ_86c4a9ce06d6d30a4154aa53c60" UNIQUE ("workspace_id"), CONSTRAINT "UQ_249146211fe79f014b09567c5eb" UNIQUE ("role_id"), CONSTRAINT "UQ_ddc3574b318b99f128d3aa05c22" UNIQUE ("user_id"), CONSTRAINT "PK_55b98efe1af767d31e909c0e6d2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "user_profile_application_company_company_id_ba45add2" ON "user_role_workspace" ("workspace_id") `);
        await queryRunner.query(`CREATE INDEX "user_profile_application_company_user_id_59aa32ff" ON "user_role_workspace" ("user_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_profile_application_user_id_profile_applicat_470f09c4_uniq" ON "user_role_workspace" ("role_id", "user_id", "workspace_id") `);
        await queryRunner.query(`CREATE INDEX "user_profile_application_c_profile_application_id_7405a125" ON "user_role_workspace" ("role_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_profile_application_company_pkey" ON "user_role_workspace" ("id") `);
        await queryRunner.query(`CREATE TABLE "user" ("id" uuid NOT NULL, "name" character varying(200), "nickname" character varying(200), "email" character varying(200) NOT NULL, "secondary_email" character varying(200), "phone" character varying(20), "gender" character varying(200), "birthday" date, "address" text, "avatar" text, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "language_id" uuid, "country" character varying(20), "ein" character varying(100), "related_user_leader_id" uuid, "email_verified" boolean NOT NULL, "time_zone" character varying(200) NOT NULL, "admission_date" date, "contract_type" character varying(100), "cpf" character varying(15), "education" character varying(100), "ethnicity" character varying(100), "hierarchical_level" character varying(100), "marital_status" character varying(100), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "user_related_user_leader_id_3b6bd128" ON "user" ("related_user_leader_id") `);
        await queryRunner.query(`CREATE INDEX "user_language_id_6dc62a7a" ON "user" ("language_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_pkey" ON "user" ("id") `);
        await queryRunner.query(`CREATE INDEX "user_email_54dc62b2_like" ON "user" ("email") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_email_54dc62b2_uniq" ON "user" ("email") `);
        await queryRunner.query(`CREATE TABLE "user_profile_workspace" ("id" uuid NOT NULL, "director" character varying(200), "manager" character varying(200), "area_of_activity" character varying(300), "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "user_id" uuid NOT NULL, "workspace_id" uuid NOT NULL, "job_position_id" uuid, "job_function_id" uuid, CONSTRAINT "UQ_4eade7d91b0db1de07277996548" UNIQUE ("user_id"), CONSTRAINT "UQ_4db6bb026a2589bb04a90e9412e" UNIQUE ("workspace_id"), CONSTRAINT "PK_738ae530e54b6482a87bd029768" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "user_profile_workspace_workspace_id_40f75c1d" ON "user_profile_workspace" ("workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_profile_workspace_user_id_workspace_id_5ab2fa27_uniq" ON "user_profile_workspace" ("user_id", "workspace_id") `);
        await queryRunner.query(`CREATE INDEX "user_profile_workspace_user_id_02f38f50" ON "user_profile_workspace" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "user_profile_workspace_job_position_id_60088b77" ON "user_profile_workspace" ("job_position_id") `);
        await queryRunner.query(`CREATE INDEX "user_profile_workspace_job_function_id_84b8bef7" ON "user_profile_workspace" ("job_function_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_profile_workspace_pkey" ON "user_profile_workspace" ("id") `);
        await queryRunner.query(`CREATE TABLE "job" ("id" uuid NOT NULL, "name" character varying(200), "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "workspace_id" uuid NOT NULL, CONSTRAINT "UQ_0a0e501362e199a2339881f4869" UNIQUE ("name"), CONSTRAINT "UQ_1efe0cfdd1cb92ac79dd90a262f" UNIQUE ("workspace_id"), CONSTRAINT "PK_98ab1c14ff8d1cf80d18703b92f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "job_workspace_id_353cd517" ON "job" ("workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "job_name_workspace_id_dbe81680_uniq" ON "job" ("name", "workspace_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "job_pkey" ON "job" ("id") `);
        await queryRunner.query(`CREATE TABLE "workspace" ("id" uuid NOT NULL, "name" character varying(200) NOT NULL, "duns_number" character varying(200), "doc_number" character varying(200), "description" text, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, "address" text, "city" character varying(200), "country" character varying(200), "icon_url" text, "logo_url" text, "post_code" character varying(200), "state" character varying(200), "theme_id" text, "icon_svg_url" text, "theme_dark" boolean NOT NULL, "company_id" uuid NOT NULL, "enable_email_notifications" boolean NOT NULL, "smtp_auth_pass" character varying(1000), "smtp_auth_user" character varying(400), "smtp_host" character varying(400), "smtp_port" integer, "smtp_reject_unauthorized" boolean, "smtp_secure" boolean, "smtp_sender_email" character varying(400), "use_own_smtp" boolean NOT NULL, "logout_url" text, "custom_color" character varying(16), "allow_create_paid_channel" boolean NOT NULL, "allow_create_paid_mission" boolean NOT NULL, "allow_create_public_channel" boolean NOT NULL, "allow_create_public_mission" boolean NOT NULL, "allow_list_paid_channel" boolean NOT NULL, "allow_list_paid_mission" boolean NOT NULL, "allow_list_public_categories" boolean NOT NULL, "allow_list_public_channel" boolean NOT NULL, "allow_list_public_mission" boolean NOT NULL, "min_performance_certificate" double precision NOT NULL, "need_approve_channel" boolean NOT NULL, "need_approve_mission" boolean NOT NULL, "custom_login_url" text, "hash_id" character varying(125), "notify_slack" boolean NOT NULL, "notify_teams" boolean NOT NULL, "enrollment_goal_duration_days" integer NOT NULL, CONSTRAINT "UQ_95ad15b52c207a2e33ffe7fc3d5" UNIQUE ("hash_id"), CONSTRAINT "PK_ca86b6f9b3be5fe26d307d09b49" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "company_pkey" ON "workspace" ("id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "workspace_hash_id_key" ON "workspace" ("hash_id") `);
        await queryRunner.query(`CREATE INDEX "workspace_hash_id_4cb6f623_like" ON "workspace" ("hash_id") `);
        await queryRunner.query(`CREATE INDEX "workspace_company_id_d3181235" ON "workspace" ("company_id") `);
        await queryRunner.query(`CREATE TABLE "company" ("id" uuid NOT NULL, "name" character varying(200) NOT NULL, "description" text, "status" boolean NOT NULL, "address" text, "city" character varying(200), "state" character varying(200), "post_code" character varying(200), "country" character varying(200), "icon_url" text, "created_date" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_date" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "company_pkey1" ON "company" ("id") `);
        await queryRunner.query(`CREATE TABLE "billing" ("id" uuid NOT NULL, "monthly_plan" integer NOT NULL, "used" integer NOT NULL, "balance" integer NOT NULL, "start_date" TIMESTAMP WITH TIME ZONE NOT NULL, "end_date" TIMESTAMP WITH TIME ZONE NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE NOT NULL, "application_id" uuid NOT NULL, "company_id" uuid, CONSTRAINT "PK_d9043caf3033c11ed3d1b29f73c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "billing_pkey" ON "billing" ("id") `);
        await queryRunner.query(`CREATE INDEX "billing_company_id_1ca9e94c" ON "billing" ("company_id") `);
        await queryRunner.query(`CREATE INDEX "billing_application_id_883a482a" ON "billing" ("application_id") `);
        await queryRunner.query(`CREATE TABLE "application" ("id" uuid NOT NULL, "name" character varying(200) NOT NULL, "description" text, "status" boolean NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_569e0c3e863ebdf5f2408ee1670" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "application_pkey" ON "application" ("id") `);
        await queryRunner.query(`CREATE TABLE "user_integration_message_hash" ("created_date" TIMESTAMP WITH TIME ZONE, "updated_date" TIMESTAMP WITH TIME ZONE NOT NULL, "id" uuid NOT NULL, "hash" text NOT NULL, "workspace_id" uuid NOT NULL, "user_email" text NOT NULL, CONSTRAINT "PK_7c2914172ef9148eb5cc7d6ca62" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "user_integration_message_hash_pkey" ON "user_integration_message_hash" ("id") `);
        await queryRunner.query(`ALTER TABLE "company_billing_plan" ADD CONSTRAINT "FK_a552046194408ac12d81226901a" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "company_billing_plan" ADD CONSTRAINT "FK_96dbeadca50b60ed7f90fdb03dd" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_workspace" ADD CONSTRAINT "FK_298a63041c3b28c9635e52101ce" FOREIGN KEY ("service_id") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service_workspace" ADD CONSTRAINT "FK_a2240b36f0ef7c2712d53c8bfaa" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "service" ADD CONSTRAINT "FK_3f08aa3ee783973df7b523521c1" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gamification_ranking_workspace" ADD CONSTRAINT "FK_2885f47a14493d6cdac429602e8" FOREIGN KEY ("service_id") REFERENCES "service"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gamification_ranking_workspace" ADD CONSTRAINT "FK_15061d30b914cfc189593a96e8b" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "idp_workspace" ADD CONSTRAINT "FK_3c3520a41d1caa49e78eb65873c" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "job_function" ADD CONSTRAINT "FK_0b2853227c9769b84f7043a8776" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role" ADD CONSTRAINT "FK_89fe3003757abd864d4ad720a1e" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_role_workspace" ADD CONSTRAINT "FK_249146211fe79f014b09567c5eb" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_role_workspace" ADD CONSTRAINT "FK_ddc3574b318b99f128d3aa05c22" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_role_workspace" ADD CONSTRAINT "FK_86c4a9ce06d6d30a4154aa53c60" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_948d2ecd168ffdbb308c1bc8a27" FOREIGN KEY ("language_id") REFERENCES "language_preference"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_ed835ff2a8826f749bd57ec3b2d" FOREIGN KEY ("related_user_leader_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" ADD CONSTRAINT "FK_b07f78b481ed375fff71af7ebe7" FOREIGN KEY ("job_function_id") REFERENCES "job_function"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" ADD CONSTRAINT "FK_031a3697eee94be46956b1d4ad2" FOREIGN KEY ("job_position_id") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" ADD CONSTRAINT "FK_4eade7d91b0db1de07277996548" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" ADD CONSTRAINT "FK_4db6bb026a2589bb04a90e9412e" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "job" ADD CONSTRAINT "FK_1efe0cfdd1cb92ac79dd90a262f" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "workspace" ADD CONSTRAINT "FK_e94fa02404fb28b2be8a3cec702" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "billing" ADD CONSTRAINT "FK_cc39343e1fabfa434012de4a089" FOREIGN KEY ("application_id") REFERENCES "application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "billing" ADD CONSTRAINT "FK_6288d67dbbb07db419dba452c23" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "billing" DROP CONSTRAINT "FK_6288d67dbbb07db419dba452c23"`);
        await queryRunner.query(`ALTER TABLE "billing" DROP CONSTRAINT "FK_cc39343e1fabfa434012de4a089"`);
        await queryRunner.query(`ALTER TABLE "workspace" DROP CONSTRAINT "FK_e94fa02404fb28b2be8a3cec702"`);
        await queryRunner.query(`ALTER TABLE "job" DROP CONSTRAINT "FK_1efe0cfdd1cb92ac79dd90a262f"`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" DROP CONSTRAINT "FK_4db6bb026a2589bb04a90e9412e"`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" DROP CONSTRAINT "FK_4eade7d91b0db1de07277996548"`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" DROP CONSTRAINT "FK_031a3697eee94be46956b1d4ad2"`);
        await queryRunner.query(`ALTER TABLE "user_profile_workspace" DROP CONSTRAINT "FK_b07f78b481ed375fff71af7ebe7"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_ed835ff2a8826f749bd57ec3b2d"`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_948d2ecd168ffdbb308c1bc8a27"`);
        await queryRunner.query(`ALTER TABLE "user_role_workspace" DROP CONSTRAINT "FK_86c4a9ce06d6d30a4154aa53c60"`);
        await queryRunner.query(`ALTER TABLE "user_role_workspace" DROP CONSTRAINT "FK_ddc3574b318b99f128d3aa05c22"`);
        await queryRunner.query(`ALTER TABLE "user_role_workspace" DROP CONSTRAINT "FK_249146211fe79f014b09567c5eb"`);
        await queryRunner.query(`ALTER TABLE "role" DROP CONSTRAINT "FK_89fe3003757abd864d4ad720a1e"`);
        await queryRunner.query(`ALTER TABLE "job_function" DROP CONSTRAINT "FK_0b2853227c9769b84f7043a8776"`);
        await queryRunner.query(`ALTER TABLE "idp_workspace" DROP CONSTRAINT "FK_3c3520a41d1caa49e78eb65873c"`);
        await queryRunner.query(`ALTER TABLE "gamification_ranking_workspace" DROP CONSTRAINT "FK_15061d30b914cfc189593a96e8b"`);
        await queryRunner.query(`ALTER TABLE "gamification_ranking_workspace" DROP CONSTRAINT "FK_2885f47a14493d6cdac429602e8"`);
        await queryRunner.query(`ALTER TABLE "service" DROP CONSTRAINT "FK_3f08aa3ee783973df7b523521c1"`);
        await queryRunner.query(`ALTER TABLE "service_workspace" DROP CONSTRAINT "FK_a2240b36f0ef7c2712d53c8bfaa"`);
        await queryRunner.query(`ALTER TABLE "service_workspace" DROP CONSTRAINT "FK_298a63041c3b28c9635e52101ce"`);
        await queryRunner.query(`ALTER TABLE "company_billing_plan" DROP CONSTRAINT "FK_96dbeadca50b60ed7f90fdb03dd"`);
        await queryRunner.query(`ALTER TABLE "company_billing_plan" DROP CONSTRAINT "FK_a552046194408ac12d81226901a"`);
        await queryRunner.query(`DROP INDEX "public"."user_integration_message_hash_pkey"`);
        await queryRunner.query(`DROP TABLE "user_integration_message_hash"`);
        await queryRunner.query(`DROP INDEX "public"."application_pkey"`);
        await queryRunner.query(`DROP TABLE "application"`);
        await queryRunner.query(`DROP INDEX "public"."billing_application_id_883a482a"`);
        await queryRunner.query(`DROP INDEX "public"."billing_company_id_1ca9e94c"`);
        await queryRunner.query(`DROP INDEX "public"."billing_pkey"`);
        await queryRunner.query(`DROP TABLE "billing"`);
        await queryRunner.query(`DROP INDEX "public"."company_pkey1"`);
        await queryRunner.query(`DROP TABLE "company"`);
        await queryRunner.query(`DROP INDEX "public"."workspace_company_id_d3181235"`);
        await queryRunner.query(`DROP INDEX "public"."workspace_hash_id_4cb6f623_like"`);
        await queryRunner.query(`DROP INDEX "public"."workspace_hash_id_key"`);
        await queryRunner.query(`DROP INDEX "public"."company_pkey"`);
        await queryRunner.query(`DROP TABLE "workspace"`);
        await queryRunner.query(`DROP INDEX "public"."job_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."job_name_workspace_id_dbe81680_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."job_workspace_id_353cd517"`);
        await queryRunner.query(`DROP TABLE "job"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_workspace_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_workspace_job_function_id_84b8bef7"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_workspace_job_position_id_60088b77"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_workspace_user_id_02f38f50"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_workspace_user_id_workspace_id_5ab2fa27_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_workspace_workspace_id_40f75c1d"`);
        await queryRunner.query(`DROP TABLE "user_profile_workspace"`);
        await queryRunner.query(`DROP INDEX "public"."user_email_54dc62b2_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."user_email_54dc62b2_like"`);
        await queryRunner.query(`DROP INDEX "public"."user_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."user_language_id_6dc62a7a"`);
        await queryRunner.query(`DROP INDEX "public"."user_related_user_leader_id_3b6bd128"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_application_company_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_application_c_profile_application_id_7405a125"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_application_user_id_profile_applicat_470f09c4_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_application_company_user_id_59aa32ff"`);
        await queryRunner.query(`DROP INDEX "public"."user_profile_application_company_company_id_ba45add2"`);
        await queryRunner.query(`DROP TABLE "user_role_workspace"`);
        await queryRunner.query(`DROP INDEX "public"."profile_application_application_id_27f10135"`);
        await queryRunner.query(`DROP INDEX "public"."profile_application_pkey"`);
        await queryRunner.query(`DROP TABLE "role"`);
        await queryRunner.query(`DROP INDEX "public"."language_preference_pkey"`);
        await queryRunner.query(`DROP TABLE "language_preference"`);
        await queryRunner.query(`DROP INDEX "public"."job_function_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."job_function_name_workspace_id_4c767397_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."job_function_workspace_id_e0b8a9d7"`);
        await queryRunner.query(`DROP TABLE "job_function"`);
        await queryRunner.query(`DROP INDEX "public"."idp_workspace_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."idp_workspace_idp_workspace_id_afc5c21e_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."idp_workspace_workspace_id_599314dd"`);
        await queryRunner.query(`DROP TABLE "idp_workspace"`);
        await queryRunner.query(`DROP INDEX "public"."gamification_ranking_workspace_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."gamification_ranking_wor_service_id_workspace_id__5e74ea63_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."gamification_ranking_workspace_service_id_bc622e08"`);
        await queryRunner.query(`DROP INDEX "public"."gamification_ranking_workspace_workspace_id_542f21d1"`);
        await queryRunner.query(`DROP TABLE "gamification_ranking_workspace"`);
        await queryRunner.query(`DROP INDEX "public"."service_application_id_a9f6d464"`);
        await queryRunner.query(`DROP INDEX "public"."service_pkey"`);
        await queryRunner.query(`DROP TABLE "service"`);
        await queryRunner.query(`DROP INDEX "public"."service_company_pkey"`);
        await queryRunner.query(`DROP INDEX "public"."service_company_service_id_company_id_242d1e51_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."service_company_service_id_0bb52aab"`);
        await queryRunner.query(`DROP INDEX "public"."service_company_company_id_2a36f3aa"`);
        await queryRunner.query(`DROP TABLE "service_workspace"`);
        await queryRunner.query(`DROP INDEX "public"."company_billing_plan_application_id_deed275d"`);
        await queryRunner.query(`DROP INDEX "public"."company_billing_plan_application_id_company_id_8f9d816f_uniq"`);
        await queryRunner.query(`DROP INDEX "public"."company_billing_plan_company_id_7dfaa71e"`);
        await queryRunner.query(`DROP INDEX "public"."company_billing_plan_pkey"`);
        await queryRunner.query(`DROP TABLE "company_billing_plan"`);
    }

}
