import { GetUserRolesUseCase } from './get-user-roles.use-case';
import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { ApplicationRoleResolver } from '../domain/roles-resolvers';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { ListUserApplicationsRolesDto } from '../presentation/dtos/list-user-applications-roles.dto';
import { RoleResolversStrategies } from './role-resolvers-tokens';
import { Logger } from '@nestjs/common';
import { UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';

const mockRoles = [
  {
    role: {
      application: { id: 'app1', name: 'App 1' },
      key: 'role1',
    },
  },
  { role: { application: { id: 'app1', name: 'App 1' }, key: 'role2' } },
  { role: { application: { id: 'app2', name: 'App 2' }, key: 'role3' } },
] as UserRoleWorkspace[];

describe('GetUserRolesUseCase', () => {
  let useCase: GetUserRolesUseCase;
  let userRolesRepository: jest.Mocked<UserRolesRepository>;
  let roleResolverStrategies: RoleResolversStrategies;

  // Suppress debug logs from the logger
  jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);
  jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

  beforeEach(() => {
    userRolesRepository = {
      getUserRoles: jest.fn().mockResolvedValue(mockRoles),
      overrideUserRoles: jest.fn(),
      findApplicationRolesById: jest.fn(),
      findUserById: jest.fn().mockImplementation((userId) => Promise.resolve({ userId })),
      deleteUserRoles: jest.fn(),
    };
    roleResolverStrategies = new Map();
    useCase = new GetUserRolesUseCase(userRolesRepository, roleResolverStrategies);
  });

  it('should group the roles by application', async () => {
    const expectedResult: ListUserApplicationsRolesDto[] = [
      {
        application: { name: 'App 1', id: 'app1' },
        roles: ['role1', 'role2'],
      },
      {
        application: { name: 'App 2', id: 'app2' },
        roles: ['role3'],
      },
    ];

    const result = await useCase.execute('userId', 'workspaceId');

    expect(userRolesRepository.findUserById).toHaveBeenCalledWith('userId');
    expect(userRolesRepository.getUserRoles).toHaveBeenCalledWith('userId', 'workspaceId');
    expect(result).toEqual(expectedResult);
  });

  it('should resolve the roles for an application with a strategy', async () => {
    const appStrategyMock = {
      getHighestRole: jest.fn(() => ['highestRole']),
    } as unknown as jest.Mocked<ApplicationRoleResolver>;
    roleResolverStrategies.set('app1', appStrategyMock);
    const expectedResult: ListUserApplicationsRolesDto[] = [
      {
        application: { name: 'App 1', id: 'app1' },
        roles: ['highestRole'],
      },
      {
        application: { name: 'App 2', id: 'app2' },
        roles: ['role3'],
      },
    ];

    const result = await useCase.execute('userId', 'workspaceId');

    expect(appStrategyMock.getHighestRole).toHaveBeenCalledWith(['role1', 'role2']);
    expect(result).toEqual(expectedResult);
  });

  it('should return an empty array if the user has no roles', async () => {
    userRolesRepository.getUserRoles.mockResolvedValue([]);

    const result = await useCase.execute('userId', 'workspaceId');

    expect(result).toEqual([]);
  });

  it('should throw an error if user is not found', async () => {
    userRolesRepository.findUserById.mockResolvedValueOnce(null);

    await expect(useCase.execute('invalidUserId', 'workspaceId')).rejects.toThrow(UserRolesUserNotFoundException);
  });
});
