import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { RolesBuilderFactory } from './roles-builders.factory';
import { SetUserRolesUseCase } from './set-user-roles.use-case';
import { SetUserApplicationsRolesDto } from '../presentation/dtos/set-user-applications-roles.dto';
import { ApplicationRolesBuilder } from '../domain/roles-builders/application-roles.builder';
import { Logger } from '@nestjs/common';
import { UserRolesNotFoundException, UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';

const DEFAULT_ROLES = [{ id: 'role-id-1' }, { id: 'role-id-2' }, { id: 'role-id-3' }];

const DEFAULT_DTO: SetUserApplicationsRolesDto[] = [
  { applicationId: 'app-id-1', roles: ['role-id-1', 'role-id-2'] },
  { applicationId: 'app-id-2', roles: ['role-id-3'] },
];

const PERSISTENT_ROLES_ID = ['keeps_admin_role_id'];

describe('SetUserRolesUseCase', () => {
  let useCase: SetUserRolesUseCase;
  let userRolesRepositoryMock: jest.Mocked<UserRolesRepository>;
  let rolesBuilderFactoryMock: jest.Mocked<RolesBuilderFactory>;
  let defaultRoleBuilderMock: jest.Mocked<ApplicationRolesBuilder>;

  beforeEach(() => {
    userRolesRepositoryMock = {
      getUserRoles: jest.fn(),
      overrideUserRoles: jest.fn(),
      deleteUserRoles: jest.fn(),
      findApplicationRolesById: jest.fn().mockResolvedValue(DEFAULT_ROLES),
      findUserById: jest.fn().mockImplementation((userId) => Promise.resolve({ userId })),
    } as jest.Mocked<UserRolesRepository>;

    defaultRoleBuilderMock = { buildRoles: jest.fn().mockImplementation((rolesIds: string[]) => rolesIds) };

    rolesBuilderFactoryMock = {
      get: jest.fn().mockReturnValue(defaultRoleBuilderMock),
    } as unknown as jest.Mocked<RolesBuilderFactory>;

    // Suppress debug logs from the logger
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

    useCase = new SetUserRolesUseCase(userRolesRepositoryMock, rolesBuilderFactoryMock, PERSISTENT_ROLES_ID);
  });

  it('should do nothing if rolesDTOs is empty', async () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';

    await useCase.execute(userId, workspaceId, []);

    expect(userRolesRepositoryMock.overrideUserRoles).not.toHaveBeenCalled();
  });

  it('should throw an exception if the user is not found', async () => {
    userRolesRepositoryMock.findUserById.mockResolvedValueOnce(null);

    await expect(useCase.execute('invalidUserId', 'workspaceId', DEFAULT_DTO)).rejects.toThrow(
      UserRolesUserNotFoundException,
    );
  });

  it('should throw an exception if one or more of the provided roles are not found', async () => {
    const invalidRolesDto: SetUserApplicationsRolesDto[] = [
      { applicationId: 'app-id-1', roles: ['invalid-role', 'role-id-2'] },
      { applicationId: 'app-id-2', roles: ['role-id-3'] },
    ];

    await expect(useCase.execute('user-id', 'workspaceId', invalidRolesDto)).rejects.toThrow(
      UserRolesNotFoundException,
    );
  });

  it('should call overrideUserRoles with the correct roles by application', async () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const rolesDTOs: SetUserApplicationsRolesDto[] = [
      { applicationId: 'app-id-1', roles: ['role-id-1', 'role-id-2'] },
      { applicationId: 'app-id-2', roles: ['role-id-3'] },
    ];

    await useCase.execute(userId, workspaceId, rolesDTOs);

    const expectedRolesMap = new Map<string, string[]>();
    expectedRolesMap.set('app-id-1', ['role-id-1', 'role-id-2']);
    expectedRolesMap.set('app-id-2', ['role-id-3']);

    expect(rolesBuilderFactoryMock.get).toHaveBeenCalledWith('app-id-1');
    expect(rolesBuilderFactoryMock.get).toHaveBeenCalledWith('app-id-2');
    expect(userRolesRepositoryMock.overrideUserRoles).toHaveBeenCalledTimes(1);
    expect(userRolesRepositoryMock.overrideUserRoles).toHaveBeenCalledWith(
      userId,
      workspaceId,
      expectedRolesMap,
      PERSISTENT_ROLES_ID,
    );
  });
});
