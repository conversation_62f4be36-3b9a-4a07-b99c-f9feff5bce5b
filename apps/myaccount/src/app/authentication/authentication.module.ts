import { Modu<PERSON> } from '@nestjs/common';
import { KeycloakModule } from '@keeps-node-apis/@core';
import { AuthenticationController } from './controllers/authentication.controller';
import { AuthenticationService } from './services/authentication.service';

@Module({
  imports: [KeycloakModule],
  controllers: [AuthenticationController],
  providers: [AuthenticationService],
})
export class AuthenticationModule {}
