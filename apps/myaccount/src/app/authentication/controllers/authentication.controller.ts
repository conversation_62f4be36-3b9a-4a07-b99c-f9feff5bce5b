import { Controller, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { LoginRequestDto, LoginResponseDto } from '../dtos/authentication.dto';
import { Public } from 'nest-keycloak-connect';
import { AuthenticationService } from '../services/authentication.service';
import { SkipTenant } from '@keeps-node-apis/@core';

/**
 * Controller responsible for authentication operations.
 */
@Controller('auth')
@ApiTags('Authentication')
export class AuthenticationController {
  constructor(private readonly authService: AuthenticationService) {}

  /**
   * Authenticates a user and returns an access token.
   *
   * @param loginDto - The credentials of the user.
   * @returns An access token upon successful authentication.
   */
  @Post('login')
  @Public()
  @SkipTenant()
  @ApiOperation({ summary: 'Authenticate user and return an access token' })
  @ApiBody({ type: LoginRequestDto })
  @ApiResponse({ status: 200, description: 'Successful login', type: LoginResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async login(@Body() loginDto: LoginRequestDto): Promise<LoginResponseDto> {
    return this.authService.login(loginDto);
  }
}
