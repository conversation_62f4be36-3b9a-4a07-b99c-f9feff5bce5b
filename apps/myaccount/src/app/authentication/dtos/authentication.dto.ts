import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * DTO for handling login request data.
 * Contains the necessary credentials for user authentication.
 */
export class LoginRequestDto {
  @ApiProperty({
    description: 'The username of the user.',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  username!: string;

  @ApiProperty({
    description: 'The password of the user.',
    example: 'SecurePassword123!',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  password!: string;
}

/**
 * DTO for handling login response data.
 * Contains the access token issued after successful authentication.
 */
export class LoginResponseDto {
  @ApiProperty({
    description: 'The access token issued by the authentication system.',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @Expose()
  access_token: string;

  constructor(access_token: string) {
    this.access_token = access_token;
  }
}
