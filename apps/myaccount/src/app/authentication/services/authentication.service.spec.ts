import { Test, TestingModule } from '@nestjs/testing';
import { KeycloakRepository } from '@keeps-node-apis/@core';
import { LoginRequestDto, LoginResponseDto } from '../dtos/authentication.dto';
import { UnauthorizedException } from '@nestjs/common';
import { AuthenticationService } from './authentication.service';

describe('AuthenticationService', () => {
  let authService: AuthenticationService;
  let keycloakRepository: KeycloakRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthenticationService,
        {
          provide: KeycloakRepository,
          useValue: {
            getUserAccessToken: jest.fn(),
          },
        },
      ],
    }).compile();

    authService = module.get<AuthenticationService>(AuthenticationService);
    keycloakRepository = module.get<KeycloakRepository>(KeycloakRepository);
  });

  it('should return an access token when login is successful', async () => {
    const mockToken = 'mocked-access-token';
    jest.spyOn(keycloakRepository, 'getUserAccessToken').mockResolvedValue(mockToken);

    const loginDto = new LoginRequestDto();
    loginDto.username = 'testuser';
    loginDto.password = 'securepassword';

    const result = await authService.login(loginDto);

    expect(result).toBeInstanceOf(LoginResponseDto);
    expect(result.access_token).toBe(mockToken);
    expect(keycloakRepository.getUserAccessToken).toHaveBeenCalledWith(loginDto.username, loginDto.password);
  });

  it('should throw UnauthorizedException when authentication fails', async () => {
    jest.spyOn(keycloakRepository, 'getUserAccessToken').mockRejectedValue(new Error('Auth failed'));

    const loginDto = new LoginRequestDto();
    loginDto.username = 'invaliduser';
    loginDto.password = 'wrongpassword';

    await expect(authService.login(loginDto)).rejects.toThrow(UnauthorizedException);
    expect(keycloakRepository.getUserAccessToken).toHaveBeenCalledWith(loginDto.username, loginDto.password);
  });
});
