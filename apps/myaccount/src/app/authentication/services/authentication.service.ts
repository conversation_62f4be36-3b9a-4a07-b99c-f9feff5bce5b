import { Injectable, UnauthorizedException } from '@nestjs/common';
import { KeycloakRepository } from '@keeps-node-apis/@core';
import { LoginRequestDto, LoginResponseDto } from '../dtos/authentication.dto';

/**
 * Service responsible for handling authentication logic.
 */
@Injectable()
export class AuthenticationService {
  constructor(private readonly keycloakRepository: KeycloakRepository) {}

  /**
   * Authenticates a user using Keycloak credentials.
   *
   * @param loginDto - The login credentials (username and password).
   * @returns A LoginResponseDto containing the access token.
   * @throws UnauthorizedException if authentication fails.
   */
  async login(loginDto: LoginRequestDto): Promise<LoginResponseDto> {
    try {
      const accessToken = await this.keycloakRepository.getUserAccessToken(loginDto.username, loginDto.password);
      return new LoginResponseDto(accessToken);
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }
}
