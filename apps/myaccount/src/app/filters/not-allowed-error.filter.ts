import { NotAllowedException } from '@keeps-node-apis/@core';
import { ExceptionFilter, Catch, ArgumentsHost, HttpExceptionBody, HttpStatus } from '@nestjs/common';
import { Response } from 'express';

@Catch(NotAllowedException)
export class NotAllowedErrorFilter implements ExceptionFilter {
  catch(exception: NotAllowedException, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const httpStatus = HttpStatus.FORBIDDEN;

    const responseBody: HttpExceptionBody = {
      statusCode: httpStatus,
      error: 'Forbidden',
      message: exception.message,
    };

    response.status(httpStatus).json(responseBody);
  }
}
