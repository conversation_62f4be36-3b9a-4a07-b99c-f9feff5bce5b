import { ExceptionFilter, Catch, ArgumentsHost, HttpExceptionBody } from '@nestjs/common';
import { EntityNotFoundError } from 'typeorm';
import { Response } from 'express';

@Catch(EntityNotFoundError)
export class EntityNotFoundErrorFilter implements ExceptionFilter {
  catch(exception: EntityNotFoundError, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const httpStatus = 404;

    const responseBody: HttpExceptionBody = {
      statusCode: httpStatus,
      error: 'Not Found',
      message: exception.message,
    };

    response.status(httpStatus).json(responseBody);
  }
}
