import { EntityNotFoundError } from 'typeorm';
import { EntityNotFoundErrorFilter } from './entity-not-found-error.filter';

describe('EntityNotFoundErrorFilter', () => {
  let filter: EntityNotFoundErrorFilter;

  beforeEach(() => {
    filter = new EntityNotFoundErrorFilter();
  });

  it('should return a JSON response with 404 status code and correct error message', () => {
    // Mocks
    const entity = Object();
    const exception = new EntityNotFoundError(entity, 'Entity not found');
    const hostSetStatusSpy = jest.fn().mockReturnThis();
    const hostJsonSpy = jest.fn();
    const host: any = {
      switchToHttp: jest.fn(() => ({
        getResponse: jest.fn(() => ({
          status: hostSetStatusSpy,
          json: hostJsonSpy,
        })),
      })),
    };

    // Call the catch method of the filter
    filter.catch(exception, host);

    // Assertions
    const expectedResponseBody = {
      statusCode: 404,
      error: 'Not Found',
      message: 'Could not find any entity of type "[object Object]" matching: "Entity not found"',
    };

    expect(host.switchToHttp).toHaveBeenCalled();
    expect(hostSetStatusSpy).toHaveBeenCalledWith(404);
    expect(hostJsonSpy).toHaveBeenCalledWith(expectedResponseBody);
  });
});
