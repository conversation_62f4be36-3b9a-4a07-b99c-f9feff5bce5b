import { Serialize } from '@keeps-node-apis/@core';
import { Controller, HttpStatus, Logger, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CONFIG_CONSTANTS } from '../../config/constants';
import { UploadAvatarResponseDto } from '../dtos/upload-avatar.response.dto';
import { IUploadFile } from '../interfaces/upload-file.interface';
import { AvatarService } from '../services/avatar.service';

/**
 * Controller responsible for handling avatar-related operations
 */
@ApiTags('Avatar')
@Controller('user-avatar')
export class AvatarController {
  private readonly logger = new Logger(AvatarController.name);

  constructor(private readonly avatarService: AvatarService) {}

  /**
   * Uploads a new avatar image
   * @param file The image file to be uploaded
   * @returns The upload result with the avatar URL
   * @throws {FileRequiredDomainException} When no file is provided
   * @throws {UnsupportedFileTypeDomainException} When file type is not supported
   * @throws {ImageProcessingDomainException} When image processing fails
   * @throws {FileUploadDomainException} When file upload fails
   */
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @Serialize(UploadAvatarResponseDto)
  @ApiOperation({ summary: 'Upload user avatar' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: `Image file (${Object.values(CONFIG_CONSTANTS.ALLOWED_IMAGES).join(', ')})`,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Avatar uploaded successfully',
    type: UploadAvatarResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'File not provided or unsupported type',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error processing or uploading avatar',
  })
  async uploadAvatar(@UploadedFile() file: Express.Multer.File): Promise<UploadAvatarResponseDto> {
    this.logger.log('Received avatar upload request');

    const uploadFile: IUploadFile = {
      fieldname: file?.fieldname,
      originalname: file?.originalname,
      mimetype: file?.mimetype,
      buffer: file?.buffer,
      size: file?.size,
    };

    const result = await this.avatarService.uploadAvatar(uploadFile);
    return { url: result };
  }
}
