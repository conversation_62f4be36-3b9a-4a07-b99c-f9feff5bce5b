import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AvatarService } from '../services/avatar.service';
import { AvatarController } from './avatar.controller';

jest.mock('../../config', () => ({
  CONFIG_CONSTANTS: {
    ALLOWED_IMAGES: {
      'image/png': 'png',
      'image/jpeg': 'jpg',
    },
  },
}));

describe('AvatarController', () => {
  let controller: AvatarController;
  let service: AvatarService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AvatarController],
      providers: [
        {
          provide: AvatarService,
          useValue: {
            uploadAvatar: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AvatarController>(AvatarController);
    service = module.get<AvatarService>(AvatarService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should upload avatar successfully', async () => {
    const mockFile = {
      fieldname: 'file',
      originalname: 'test.png',
      encoding: '7bit',
      mimetype: 'image/png',
      buffer: Buffer.from('test image data'),
      size: 1024,
    } as any;

    const mockResponse = {
      url: 'https://s3.amazonaws.com/user-avatar/uuid.png',
    };

    jest.spyOn(service, 'uploadAvatar').mockResolvedValue(mockResponse.url);

    const result = await controller.uploadAvatar(mockFile);

    expect(service.uploadAvatar).toHaveBeenCalledWith({
      fieldname: mockFile.fieldname,
      originalname: mockFile.originalname,
      mimetype: mockFile.mimetype,
      buffer: mockFile.buffer,
      size: mockFile.size,
    });
    expect(result).toEqual({ url: mockResponse.url });
  });

  it('should handle upload errors', async () => {
    const mockFile = {
      fieldname: 'file',
      originalname: 'test.pdf',
      mimetype: 'application/pdf',
      buffer: Buffer.from('test file data'),
      size: 1024,
    } as any;

    jest.spyOn(service, 'uploadAvatar').mockRejectedValue(new Error('Unsupported image type'));

    await expect(controller.uploadAvatar(mockFile)).rejects.toThrow('Unsupported image type');
    expect(service.uploadAvatar).toHaveBeenCalledWith({
      fieldname: mockFile.fieldname,
      originalname: mockFile.originalname,
      encoding: mockFile.encoding,
      mimetype: mockFile.mimetype,
      buffer: mockFile.buffer,
      size: mockFile.size,
    });
  });
});
