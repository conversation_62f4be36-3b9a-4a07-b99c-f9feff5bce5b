/**
 * Interface representing the required properties for a file upload
 */
export interface IUploadFile {
  /**
   * The name of the form field that contained the file
   * @example "avatar"
   */
  fieldname: string;

  /**
   * The original name of the file on the user's computer
   * @example "profile-picture.png"
   */
  originalname: string;

  /**
   * The MIME type of the file
   * @example "image/png"
   */
  mimetype: string;

  /**
   * The Buffer containing the entire file
   */
  buffer: Buffer;

  /**
   * The size of the file in bytes
   * @example 12345
   */
  size: number;
}
