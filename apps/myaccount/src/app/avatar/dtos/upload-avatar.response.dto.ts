import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * Represents the result of an avatar upload operation
 */
export class UploadAvatarResponseDto {
  /**
   * The URL where the avatar can be accessed
   * @example "https://s3.amazonaws.com/user-avatar/123e4567-e89b-12d3-a456-426614174000.png"
   */
  @ApiProperty({
    description: 'The URL where the avatar can be accessed',
    example: 'https://s3.amazonaws.com/user-avatar/123e4567-e89b-12d3-a456-426614174000.png',
  })
  @Expose()
  url: string;
}
