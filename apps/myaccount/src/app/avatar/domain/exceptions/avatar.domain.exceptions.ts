import { DomainException } from '../../../@core/common/exceptions/base.exception';

export class AvatarDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `AVATAR_${errorCode}`, details);
  }
}

export class FileRequiredDomainException extends AvatarDomainException {
  constructor() {
    super('File is required', 'FILE_REQUIRED');
  }
}

export class UnsupportedFileTypeDomainException extends AvatarDomainException {
  constructor(supportedTypes: string[]) {
    super('Unsupported file type', 'UNSUPPORTED_FILE_TYPE', { supportedTypes });
  }
}

export class FileSizeExceededDomainException extends AvatarDomainException {
  constructor(maxSizeInBytes: number, actualSizeInBytes: number) {
    super('File size exceeds maximum allowed', 'FILE_SIZE_EXCEEDED', {
      maxSizeInBytes,
      actualSizeInBytes,
      maxSizeInMB: Math.round(maxSizeInBytes / (1024 * 1024)),
      actualSizeInMB: Math.round(actualSizeInBytes / (1024 * 1024)),
    });
  }
}

export class ImageProcessingDomainException extends AvatarDomainException {
  constructor(message: string, details?: Record<string, any>) {
    super(`Error processing image: ${message}`, 'PROCESSING_ERROR', details);
  }
}

export class FileUploadDomainException extends AvatarDomainException {
  constructor(message: string, details?: Record<string, any>) {
    super(`Error uploading file: ${message}`, 'UPLOAD_ERROR', details);
  }
}
