import { Test, TestingModule } from '@nestjs/testing';
import { ImageProcessorService } from './image-processor.service';
import * as sharp from 'sharp';
import { ImageProcessingDomainException } from '../domain/exceptions/avatar.domain.exceptions';

jest.mock('sharp');

describe('ImageProcessorService', () => {
  let service: ImageProcessorService;
  let mockSharpInstance: jest.Mocked<sharp.Sharp>;

  beforeEach(async () => {
    mockSharpInstance = {
      resize: jest.fn().mockReturnThis(),
      toBuffer: jest.fn(),
    } as any;

    (sharp as unknown as jest.Mock).mockReturnValue(mockSharpInstance);

    const module: TestingModule = await Test.createTestingModule({
      providers: [ImageProcessorService],
    }).compile();

    service = module.get<ImageProcessorService>(ImageProcessorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processImage', () => {
    const mockBuffer = Buffer.from('test image data');
    const mockWidth = 200;
    const mockHeight = 200;
    const mockProcessedBuffer = Buffer.from('processed image data');
    const mockOutputInfo: sharp.OutputInfo = {
      format: 'png',
      width: mockWidth,
      height: mockHeight,
      channels: 4 as const,
      size: mockProcessedBuffer.length,
      premultiplied: true,
    };

    it('should successfully process image', async () => {
      mockSharpInstance.toBuffer.mockResolvedValue({ data: mockProcessedBuffer, info: mockOutputInfo });

      const result = await service.processImage(mockBuffer, mockWidth, mockHeight);

      expect(sharp).toHaveBeenCalledWith(mockBuffer);
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(mockWidth, mockHeight, {
        fit: 'cover',
        position: 'center',
      });
      expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
      expect(result).toEqual({ data: mockProcessedBuffer, info: mockOutputInfo });
    });

    it('should propagate image processing errors', async () => {
      const error = new Error('Image processing failed');
      mockSharpInstance.toBuffer.mockRejectedValue(error);

      const expectedError = new ImageProcessingDomainException(error.message);

      await expect(service.processImage(mockBuffer, mockWidth, mockHeight)).rejects.toThrow(expectedError);
      expect(sharp).toHaveBeenCalledWith(mockBuffer);
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(mockWidth, mockHeight, {
        fit: 'cover',
        position: 'center',
      });
      expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
    });

    it('should handle invalid image buffer', async () => {
      const invalidBuffer = Buffer.from('invalid image data');
      mockSharpInstance.toBuffer.mockRejectedValue(new Error('Invalid image data'));

      await expect(service.processImage(invalidBuffer, mockWidth, mockHeight)).rejects.toThrow('Invalid image data');
    });

    it('should maintain aspect ratio when dimensions are different', async () => {
      const differentWidth = 300;
      const differentHeight = 200;
      mockSharpInstance.toBuffer.mockResolvedValue({ data: mockProcessedBuffer, info: mockOutputInfo });

      await service.processImage(mockBuffer, differentWidth, differentHeight);

      expect(mockSharpInstance.resize).toHaveBeenCalledWith(differentWidth, differentHeight, {
        fit: 'cover',
        position: 'center',
      });
    });
  });
});
