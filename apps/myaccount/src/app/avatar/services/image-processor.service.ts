import { Injectable } from '@nestjs/common';
import { IAvatarImageProcessor } from '../interfaces/avatar-image-processor.interface';
import { ImageProcessingDomainException } from '../domain/exceptions/avatar.domain.exceptions';
import sharp from 'sharp';

@Injectable()
export class ImageProcessorService implements IAvatarImageProcessor {
  async processImage(buffer: Buffer, width: number, height: number): Promise<Buffer> {
    try {
      return await sharp(buffer)
        .resize(width, height, {
          fit: 'cover',
          position: 'center',
        })
        .toBuffer();
    } catch (error) {
      throw new ImageProcessingDomainException(error.message, {
        width,
        height,
        originalError: error.message,
      });
    }
  }
}
