import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { default as AppConfig } from '../../config/modules/app.config';
import { AVATAR_IMAGE_PROCESSOR, AVATAR_STORAGE } from '../constants/injection-tokens';
import {
  FileRequiredDomainException,
  FileSizeExceededDomainException,
  FileUploadDomainException,
  ImageProcessingDomainException,
  UnsupportedFileTypeDomainException,
} from '../domain/exceptions/avatar.domain.exceptions';
import { IAvatarImageProcessor } from '../interfaces/avatar-image-processor.interface';
import { IAvatarStorage } from '../interfaces/avatar-storage.interface';
import { IUploadFile } from '../interfaces/upload-file.interface';

@Injectable()
export class AvatarService {
  private readonly logger = new Logger(AvatarService.name);

  constructor(
    @Inject(AVATAR_STORAGE)
    private readonly avatarStorage: IAvatarStorage,
    @Inject(AVATAR_IMAGE_PROCESSOR)
    private readonly imageProcessor: IAvatarImageProcessor,
    @Inject(AppConfig.KEY)
    private config: ConfigType<typeof AppConfig>,
  ) {}

  async uploadAvatar(file: IUploadFile): Promise<string> {
    this.logger.log(`Starting avatar upload process for file: ${file?.originalname}`);

    if (!file) {
      this.logger.warn('Attempt to upload avatar without providing a file');
      throw new FileRequiredDomainException();
    }

    if (!this.config.avatar.storage.allowedTypes.includes(file.mimetype)) {
      this.logger.warn(`Unsupported file type attempted: ${file.mimetype}`);
      throw new UnsupportedFileTypeDomainException(this.config.avatar.storage.allowedTypes);
    }

    if (file.size > this.config.avatar.storage.maxSizeInBytes) {
      this.logger.warn(`File size exceeds maximum allowed: ${file.size} bytes`);
      throw new FileSizeExceededDomainException(this.config.avatar.storage.maxSizeInBytes, file.size);
    }

    try {
      this.logger.debug(
        `Processing image with size ${this.config.avatar.image.defaultSize}x${this.config.avatar.image.defaultSize}`,
      );
      const processedImageBuffer = await this.imageProcessor.processImage(
        file.buffer,
        this.config.avatar.image.defaultSize,
        this.config.avatar.image.defaultSize,
      );

      const fileName = `${uuidv4()}.${this.config.avatar.image.format}`;
      const filePath = `${this.config.avatar.storage.basePath}/${fileName}`;

      this.logger.debug(`Uploading processed image to storage: ${filePath}`);
      const result = await this.avatarStorage.uploadBuffer(
        processedImageBuffer,
        filePath,
        `image/${this.config.avatar.image.format}`,
      );

      this.logger.log(`Successfully uploaded avatar: ${result.url}`);
      return result.url;
    } catch (error) {
      const errorPhase = this.determineErrorPhase(error);

      if (errorPhase === 'processing') {
        this.logger.error(`Error processing image: ${error.message}`, error.stack);
        throw new ImageProcessingDomainException(error.message, {
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
        });
      }

      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      throw new FileUploadDomainException(error.message, {
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
      });
    }
  }

  private determineErrorPhase(error: Error): 'processing' | 'upload' {
    const processingErrorKeywords = ['process', 'image', 'resize', 'cut', 'buffer'];
    return processingErrorKeywords.some((keyword) => error.message.toLowerCase().includes(keyword.toLowerCase()))
      ? 'processing'
      : 'upload';
  }
}
