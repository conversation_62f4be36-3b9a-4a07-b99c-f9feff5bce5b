import { Test, TestingModule } from '@nestjs/testing';
import { S3AvatarStorageService } from './s3-avatar-storage.service';
import { S3Uploader } from '@keeps-node-apis/@core';

describe('S3AvatarStorageService', () => {
  let service: S3AvatarStorageService;
  let s3Uploader: jest.Mocked<S3Uploader>;

  beforeEach(async () => {
    s3Uploader = {
      uploadFilePath: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3AvatarStorageService,
        {
          provide: S3Uploader,
          useValue: s3Uploader,
        },
      ],
    }).compile();

    service = module.get<S3AvatarStorageService>(S3AvatarStorageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadFile', () => {
    const mockFilePath = 'test/path/image.png';
    const mockKey = 'user-avatar/test.png';
    const mockContentType = 'image/png';
    const mockResponse = {
      name: 'test.png',
      url: 'https://s3.amazonaws.com/user-avatar/test.png',
    };

    it('should successfully upload file to S3', async () => {
      s3Uploader.uploadFilePath.mockResolvedValue(mockResponse);

      const result = await service.uploadFile(mockFilePath, mockKey, mockContentType);

      expect(s3Uploader.uploadFilePath).toHaveBeenCalledWith(mockFilePath, mockKey, mockContentType);
      expect(result).toEqual(mockResponse);
    });

    it('should propagate S3 upload errors', async () => {
      const error = new Error('S3 upload failed');
      s3Uploader.uploadFilePath.mockRejectedValue(error);

      await expect(service.uploadFile(mockFilePath, mockKey, mockContentType)).rejects.toThrow(error);
      expect(s3Uploader.uploadFilePath).toHaveBeenCalledWith(mockFilePath, mockKey, mockContentType);
    });
  });
});
