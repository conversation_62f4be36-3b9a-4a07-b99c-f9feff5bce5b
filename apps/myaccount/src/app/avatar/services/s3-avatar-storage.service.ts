import { Injectable } from '@nestjs/common';
import { S3Uploader, FileType } from '@keeps-node-apis/@core';
import { IAvatarStorage } from '../interfaces/avatar-storage.interface';
import { CONFIG_CONSTANTS } from '../../config/constants';
import { UnsupportedFileTypeDomainException } from '../domain/exceptions/avatar.domain.exceptions';

@Injectable()
export class S3AvatarStorageService implements IAvatarStorage {
  constructor(private readonly s3Uploader: S3Uploader) {}

  async uploadFile(filePath: string, key: string, contentType: string): Promise<{ name: string; url: string }> {
    return await this.s3Uploader.uploadFilePath(filePath, key, contentType);
  }

  async uploadBuffer(buffer: Buffer, key: string, contentType: string): Promise<{ name: string; url: string }> {
    const fileType = this.getFileType(contentType);
    const url = await this.s3Uploader.uploadFile(key, buffer, fileType);
    return { name: key, url };
  }

  private getFileType(contentType: string): FileType {
    const extension = CONFIG_CONSTANTS.ALLOWED_IMAGES[contentType];
    if (!extension) {
      throw new UnsupportedFileTypeDomainException(Object.keys(CONFIG_CONSTANTS.ALLOWED_IMAGES));
    }
    return extension.toUpperCase() as FileType;
  }
}
