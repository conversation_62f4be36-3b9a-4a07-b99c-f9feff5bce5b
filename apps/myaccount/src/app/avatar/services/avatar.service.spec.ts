import { Test, TestingModule } from '@nestjs/testing';
import { AvatarService } from './avatar.service';
import { AVATAR_STORAGE, AVATAR_IMAGE_PROCESSOR } from '../constants/injection-tokens';
import { IAvatarStorage } from '../interfaces/avatar-storage.interface';
import { IAvatarImageProcessor } from '../interfaces/avatar-image-processor.interface';
import {
  FileRequiredDomainException,
  UnsupportedFileTypeDomainException,
  ImageProcessingDomainException,
  FileUploadDomainException,
  FileSizeExceededDomainException,
} from '../domain/exceptions/avatar.domain.exceptions';
import { IUploadFile } from '../interfaces/upload-file.interface';

import { Logger } from '@nestjs/common';
import { default as AppConfig } from '../../config/modules/app.config';

describe('AvatarService', () => {
  let service: AvatarService;
  let mockStorage: jest.Mocked<IAvatarStorage>;
  let mockImageProcessor: jest.Mocked<IAvatarImageProcessor>;

  beforeEach(async () => {
    // Suppress logger during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);

    mockStorage = {
      uploadBuffer: jest.fn(),
    };

    mockImageProcessor = {
      processImage: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AvatarService,
        {
          provide: AVATAR_STORAGE,
          useValue: mockStorage,
        },
        {
          provide: AVATAR_IMAGE_PROCESSOR,
          useValue: mockImageProcessor,
        },
        {
          provide: AppConfig.KEY,
          useValue: {
            avatar: {
              storage: {
                allowedTypes: ['image/png', 'image/jpeg'],
                maxSizeInBytes: 5242880, // 5MB
                basePath: 'user-avatar',
              },
              image: {
                defaultSize: 200,
                format: 'png',
              },
            },
          },
        },
      ],
    }).compile();

    service = module.get<AvatarService>(AvatarService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadAvatar', () => {
    const mockFile: IUploadFile = {
      fieldname: 'file',
      originalname: 'test.png',
      mimetype: 'image/png',
      buffer: Buffer.from('test image data'),
      size: 1024,
    };

    const mockProcessedBuffer = Buffer.from('processed image data');
    const mockUploadResponse = {
      name: 'uuid.png',
      url: 'https://s3.amazonaws.com/user-avatar/uuid.png',
    };

    it('should successfully upload an avatar', async () => {
      mockImageProcessor.processImage.mockResolvedValue(mockProcessedBuffer);
      mockStorage.uploadBuffer.mockResolvedValue(mockUploadResponse);

      const result = await service.uploadAvatar(mockFile);

      expect(mockImageProcessor.processImage).toHaveBeenCalledWith(mockFile.buffer, 200, 200);
      expect(mockStorage.uploadBuffer).toHaveBeenCalledWith(
        mockProcessedBuffer,
        expect.stringMatching(/user-avatar\/.*\.png/),
        'image/png',
      );
      expect(result).toEqual(mockUploadResponse.url);
    });

    it('should throw FileRequiredDomainException when no file is provided', async () => {
      await expect(service.uploadAvatar(null)).rejects.toThrow(FileRequiredDomainException);
      expect(mockImageProcessor.processImage).not.toHaveBeenCalled();
      expect(mockStorage.uploadBuffer).not.toHaveBeenCalled();
    });

    it('should throw UnsupportedFileTypeDomainException for unsupported file types', async () => {
      const unsupportedFile: IUploadFile = {
        ...mockFile,
        mimetype: 'application/pdf',
      };

      await expect(service.uploadAvatar(unsupportedFile)).rejects.toThrow(UnsupportedFileTypeDomainException);
      expect(mockImageProcessor.processImage).not.toHaveBeenCalled();
      expect(mockStorage.uploadBuffer).not.toHaveBeenCalled();
    });

    it('should throw FileSizeExceededDomainException when file size exceeds limit', async () => {
      const oversizedFile: IUploadFile = {
        ...mockFile,
        size: 5242881, // 5MB + 1 byte
      };

      await expect(service.uploadAvatar(oversizedFile)).rejects.toThrow(FileSizeExceededDomainException);
      expect(mockImageProcessor.processImage).not.toHaveBeenCalled();
      expect(mockStorage.uploadBuffer).not.toHaveBeenCalled();
    });

    it('should throw ImageProcessingDomainException when image processing fails', async () => {
      mockImageProcessor.processImage.mockRejectedValue(new Error('Error processing image: Invalid image format'));

      await expect(service.uploadAvatar(mockFile)).rejects.toThrow(ImageProcessingDomainException);
      expect(mockStorage.uploadBuffer).not.toHaveBeenCalled();
    });

    it('should throw FileUploadDomainException when upload fails', async () => {
      mockImageProcessor.processImage.mockResolvedValue(mockProcessedBuffer);
      mockStorage.uploadBuffer.mockRejectedValue(new Error('Failed to upload to S3'));

      await expect(service.uploadAvatar(mockFile)).rejects.toThrow(FileUploadDomainException);
      expect(mockImageProcessor.processImage).toHaveBeenCalled();
    });

    describe('error phase detection', () => {
      it('should detect processing errors correctly', async () => {
        const processingErrors = [
          'Error processing image',
          'Failed to resize image',
          'Invalid image buffer',
          'Error during image cut operation',
          'Cannot process corrupted image',
        ];

        for (const errorMessage of processingErrors) {
          mockImageProcessor.processImage.mockRejectedValue(new Error(errorMessage));
          await expect(service.uploadAvatar(mockFile)).rejects.toThrow(ImageProcessingDomainException);
        }
      });

      it('should detect upload errors correctly', async () => {
        const uploadErrors = ['S3 connection failed', 'Network error', 'Invalid credentials', 'Bucket not found'];

        for (const errorMessage of uploadErrors) {
          mockImageProcessor.processImage.mockResolvedValue(mockProcessedBuffer);
          mockStorage.uploadBuffer.mockRejectedValue(new Error(errorMessage));
          await expect(service.uploadAvatar(mockFile)).rejects.toThrow(FileUploadDomainException);
        }
      });
    });
  });
});
