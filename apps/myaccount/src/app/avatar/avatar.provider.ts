import { AVATAR_IMAGE_PROCESSOR, AVATAR_STORAGE } from './constants/injection-tokens';
import { AvatarService } from './services/avatar.service';
import { ImageProcessorService } from './services/image-processor.service';
import { S3AvatarStorageService } from './services/s3-avatar-storage.service';

export const PROVIDERS = [
  AvatarService,
  {
    provide: AVATAR_STORAGE,
    useClass: S3AvatarStorageService,
  },
  {
    provide: AVATAR_IMAGE_PROCESSOR,
    useClass: ImageProcessorService,
  },
];

export const EXPORTS = [AvatarService];
