import { AwsModule } from '@keeps-node-apis/@core';
import { Module } from '@nestjs/common';
import { EntitiesModule } from '../entities/entities.module';
import { EXPORTS, PROVIDERS } from './avatar.provider';
import { AvatarController } from './controllers/avatar.controller';

@Module({
  imports: [EntitiesModule, AwsModule],
  controllers: [AvatarController],
  providers: PROVIDERS,
  exports: EXPORTS,
})
export class AvatarModule {}
