import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationServicesService } from './application-services.service';
import { ApplicationServicesRepository } from '../interfaces/application-services-repository';
import { Chance } from 'chance';

describe('ApplicationServicesService', () => {
  let service: ApplicationServicesService;
  let repository: ApplicationServicesRepository;
  const chance = new Chance();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationServicesService,
        {
          provide: ApplicationServicesRepository,
          useValue: { getApplicationServicesByWorkspace: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<ApplicationServicesService>(ApplicationServicesService);
    repository = module.get<ApplicationServicesRepository>(ApplicationServicesRepository);
  });

  it('should call ApplicationServicesRepository with the provided workspaceId', async () => {
    const workspaceId = chance.guid();

    await service.getApplicationServicesByWorkspace(workspaceId);

    expect(repository.getApplicationServicesByWorkspace).toHaveBeenCalledWith(workspaceId);
  });
});
