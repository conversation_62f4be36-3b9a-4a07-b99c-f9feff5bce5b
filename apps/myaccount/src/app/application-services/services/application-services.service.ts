import { Injectable } from '@nestjs/common';
import { Service } from '../../entities/service.entity';
import { ApplicationServicesRepository } from '../interfaces/application-services-repository';

@Injectable()
export class ApplicationServicesService {
  constructor(private readonly applicationServicesRepository: ApplicationServicesRepository) {}

  async getApplicationServicesByWorkspace(workspaceId: string): Promise<Service[]> {
    return this.applicationServicesRepository.getApplicationServicesByWorkspace(workspaceId);
  }
}
