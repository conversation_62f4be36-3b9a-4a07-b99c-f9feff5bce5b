import { ApplicationServicesService } from './services/application-services.service';
import { Provider } from '@nestjs/common';
import { ApplicationServicesRepository } from './interfaces/application-services-repository';
import { ApplicationServicesTypeOrmRepository } from './repositories/application-services-type-orm.repository';

export const PROVIDERS: Provider[] = [
  ApplicationServicesService,
  {
    provide: ApplicationServicesRepository,
    useClass: ApplicationServicesTypeOrmRepository,
  },
];
