import { Injectable } from '@nestjs/common';
import { ApplicationServicesRepository } from '../interfaces/application-services-repository';
import { Service } from '../../entities/service.entity';
import { DataSource, Repository } from 'typeorm';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';

@Injectable()
export class ApplicationServicesTypeOrmRepository implements ApplicationServicesRepository {
  private readonly repository: Repository<ServiceWorkspace>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(ServiceWorkspace);
  }

  async getApplicationServicesByWorkspace(workspaceId: string): Promise<Service[]> {
    const serviceWorkspaces = await this.repository
      .createQueryBuilder('service_workspace')
      .leftJoinAndSelect('service_workspace.service', 'services')
      .where('service_workspace.workspace_id = :workspaceId', { workspaceId })
      .andWhere('service_workspace.status = true')
      .getMany();

    return serviceWorkspaces?.map((service) => service.service);
  }
}
