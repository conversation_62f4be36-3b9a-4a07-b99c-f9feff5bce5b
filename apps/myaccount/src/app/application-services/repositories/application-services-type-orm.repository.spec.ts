import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationServicesTypeOrmRepository } from './application-services-type-orm.repository';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Chance } from 'chance';
import { Service } from '../../entities/service.entity';

describe('ApplicationServicesTypeOrmRepository', () => {
  const chance = new Chance();
  let repository: ApplicationServicesTypeOrmRepository;
  let typeOrmRepositoryMock: jest.Mocked<Repository<ServiceWorkspace>>;
  let queryBuilderMock: jest.Mocked<SelectQueryBuilder<ServiceWorkspace>>;

  beforeEach(async () => {
    queryBuilderMock = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    } as unknown as jest.Mocked<SelectQueryBuilder<ServiceWorkspace>>;

    typeOrmRepositoryMock = {
      createQueryBuilder: jest.fn().mockReturnValue(queryBuilderMock),
    } as unknown as jest.Mocked<Repository<ServiceWorkspace>>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationServicesTypeOrmRepository,
        {
          provide: DataSource,
          useValue: { getRepository: jest.fn().mockReturnValue(typeOrmRepositoryMock) },
        },
      ],
    }).compile();

    repository = module.get<ApplicationServicesTypeOrmRepository>(ApplicationServicesTypeOrmRepository);
  });

  it('should query the application services for a workspace', async () => {
    const workspaceId = chance.guid();
    const mockServices = [{ id: chance.guid(), name: chance.name() }] as Service[];
    const mockQueryResult = [{ id: chance.guid(), service: mockServices.at(0) }] as ServiceWorkspace[];
    queryBuilderMock.getMany.mockResolvedValueOnce(mockQueryResult);

    const result = await repository.getApplicationServicesByWorkspace(workspaceId);

    expect(typeOrmRepositoryMock.createQueryBuilder).toHaveBeenCalledWith('service_workspace');
    expect(queryBuilderMock.leftJoinAndSelect).toHaveBeenCalledWith('service_workspace.service', 'services');
    expect(queryBuilderMock.where).toHaveBeenCalledWith('service_workspace.workspace_id = :workspaceId', {
      workspaceId,
    });
    expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('service_workspace.status = true');
    expect(queryBuilderMock.getMany).toHaveBeenCalled();
    expect(result).toEqual(mockServices);
  });
});
