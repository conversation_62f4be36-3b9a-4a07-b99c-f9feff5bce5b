import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationServicesController } from './application-services.controller';
import { ApplicationServicesService } from '../../services/application-services.service';
import { Chance } from 'chance';

describe('ApplicationServicesController', () => {
  let controller: ApplicationServicesController;
  let service: ApplicationServicesService;
  const chance = new Chance();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApplicationServicesController],
      providers: [{ provide: ApplicationServicesService, useValue: { getApplicationServicesByWorkspace: jest.fn() } }],
    }).compile();

    controller = module.get<ApplicationServicesController>(ApplicationServicesController);
    service = module.get<ApplicationServicesService>(ApplicationServicesService);
  });

  it('should call getApplicationServicesByWorkspace with the provided x-client header', async () => {
    const xClient = chance.guid();

    await controller.getApplicationServicesByWorkspace(xClient);

    expect(service.getApplicationServicesByWorkspace).toHaveBeenCalledWith(xClient);
  });
});
