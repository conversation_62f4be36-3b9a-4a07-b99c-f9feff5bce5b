import { <PERSON>, Get, HttpStatus, Headers } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ApplicationServicesService } from '../../services/application-services.service';
import { ApplicationServiceDto } from '../dtos/application-service.dto';
import { Serialize } from '@keeps-node-apis/@core';

@ApiTags('Application Services')
@Controller('application-services')
export class ApplicationServicesController {
  constructor(private readonly applicationServicesService: ApplicationServicesService) {}

  @Get()
  @Serialize(ApplicationServiceDto)
  @ApiOperation({ summary: 'Fetch the list of services for all applications related to the current workspace' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list with all application services of the current workspace',
    type: ApplicationServiceDto,
  })
  getApplicationServicesByWorkspace(@Headers('x-client') workspaceId: string): Promise<ApplicationServiceDto[]> {
    return this.applicationServicesService.getApplicationServicesByWorkspace(workspaceId);
  }
}
