import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class ApplicationServiceDto {
  @ApiProperty({ description: 'Service ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Service name', example: 'Regulatory Compliance' })
  @Expose()
  name: string;

  @ApiProperty({ description: 'Service status', example: 'The service status' })
  @Expose()
  status: boolean;
}
