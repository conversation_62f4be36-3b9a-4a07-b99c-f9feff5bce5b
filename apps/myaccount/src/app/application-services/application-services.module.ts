import { Module } from '@nestjs/common';
import { PROVIDERS } from './application-services.provider';
import { ApplicationServicesController } from './presentation/controllers/application-services.controller';
import { EntitiesModule } from '../entities/entities.module';

@Module({
  imports: [EntitiesModule],
  providers: PROVIDERS,
  controllers: [ApplicationServicesController],
  exports: PROVIDERS,
})
export class ApplicationServicesModule {}
