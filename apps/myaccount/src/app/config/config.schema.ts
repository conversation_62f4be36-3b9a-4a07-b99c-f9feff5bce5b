import Joi from 'joi';

export default Joi.object({
  NODE_ENV: Joi.string().valid('development', 'stage', 'production').required(),
  PORT: Joi.number().default(3000),
  CRYPTO_SECRET_KEY: Joi.string().required(),

  // application ids
  MYACCOUNT_ID: Joi.string().default('ad7e5ad2-1552-43ab-a471-710954f0e66a'),
  KONQUEST_ID: Joi.string().default('0abf08ea-d252-4d7c-ab45-ab3f9135c288'),
  SMARTZAP_ID: Joi.string().default('84d6715e-9b75-436d-ad44-b74c5a7f6729'),
  ANALYTICS_ID: Joi.string().default('c2928f23-a5a6-4f59-94a7-7e409cf1d4f4'),

  // my account account_admin role id
  MYACCOUNT_ACCOUNT_ADMIN_ROLE_ID: Joi.string().default('3b16b975-0297-4edf-950b-e3700b0d0d01'),
  KEEPS_ADMIN_ROLE_ID: Joi.string().default('e67234f4-957b-483d-badc-2fbcd6cd4173'),

  // rabbitmq
  RABBITMQ_BROKER_URL: Joi.string().uri().required(),
  QUEUE_SMARTZAP_COMPANY: Joi.string().valid('smartzap-companies-stage', 'smartzap-companies').required(),

  // redis
  REDIS_HOST: Joi.string().required(),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().default('').empty(''),

  // database
  DB_USER: Joi.string().default('postgres'),
  DB_PASS: Joi.string().default('postgres'),
  DB_NAME: Joi.string().valid('myaccount_db', 'myaccount_dev_db').required(),
  DB_HOST: Joi.string().default('localhost'),
  DB_PORT: Joi.number().default(5432),
  DB_DEBUG: Joi.boolean().default(false),
  DB_MIGRATIONS_RUN: Joi.boolean().default(false),

  // notification
  NOTIFICATION_API_URL: Joi.string().uri().required(),

  // s3
  AWS_S3_REGION: Joi.string().default('us-east-1'),
  AWS_S3_BUCKET: Joi.string().valid('keeps-media-stage', 'keeps-media').required(),
  AWS_S3_BUCKET_PATH: Joi.string().default('myaccount'),
  AWS_S3_CDN_URL: Joi.string().uri().valid('https://media.keepsdev.com', 'https://media-stage.keepsdev.com').required(),
  AWS_S3_ACCESS_KEY_ID: Joi.string().required(),
  AWS_S3_SECRET_ACCESS_KEY: Joi.string().required(),

  // auth
  AUTH_URL: Joi.string().default('https://iam.keepsdev.com/auth/'),
  AUTH_REALM: Joi.string().valid('keeps-dev', 'keeps').required(),
  AUTH_CLIENT_ID: Joi.string().default('myaccount'),
  AUTH_CLIENT_SECRET: Joi.string().required(),
  AUTH_REALM_PUBLIC_KEY: Joi.string().required(),
  AUTH_DEBUG: Joi.boolean().default(false),

  // aplications WEB urls
  MYACCOUNT_WEB_URL: Joi.string().uri().default('https://myaccount-stage.keepsdev.com').required(),
});
