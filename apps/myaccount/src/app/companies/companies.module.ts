import { Module } from '@nestjs/common';
import { EntitiesModule } from '../entities/entities.module';
import { CompaniesController } from './companies.controller';
import providers from './companies.providers';
import { CompaniesService } from './companies.service';

@Module({
  imports: [EntitiesModule],
  controllers: [CompaniesController],
  exports: [CompaniesService],
  providers: providers,
})
export class CompaniesModule {}
