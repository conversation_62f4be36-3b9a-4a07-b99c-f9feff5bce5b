import ICompaniesRepository from './companies.repository.interface';
import { Company } from '../entities/company.entity';

export const CompaniesRepository: Pick<ICompaniesRepository, any> = {
  async findAllowed(userId: string): Promise<Company[]> {
    return this.createQueryBuilder('company')
      .innerJoin('company.workspaces', 'workspace')
      .innerJoin(
        'workspace.userRoleWorkspaces',
        'userRoleWorkspace',
        'userRoleWorkspace.userId = :userId AND userRoleWorkspace.status = true',
        { userId },
      )
      .leftJoinAndSelect('company.workspaces', 'companyWorkspaces')
      .getMany();
  },
};
