import { Provider } from '@nestjs/common';
import { CompaniesService } from './companies.service';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { Company } from '../entities/company.entity';
import { DataSource } from 'typeorm';
import { CompaniesRepository } from './companies.repository';

const COMPANIES_REPOSITORY: Provider = {
  provide: getRepositoryToken(Company),
  useFactory(datasource: DataSource) {
    return datasource.getRepository(Company).extend(CompaniesRepository);
  },
  inject: [getDataSourceToken()],
};

export default [CompaniesService, COMPANIES_REPOSITORY];
