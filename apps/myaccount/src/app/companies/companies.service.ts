import { Injectable } from '@nestjs/common';
import ICompaniesRepository from './companies.repository.interface';
import { Company } from '../entities/company.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { CreateCompanyDto } from './dto/create-company.dto';
import { CompanyDto } from './dto/company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';

@Injectable()
export class CompaniesService {
  constructor(@InjectRepository(Company) private readonly repo: ICompaniesRepository) {}

  /**
   * Creates a new company with the provided data
   * @param createCompanyDto - Data for creating a new company
   * @returns Promise<Company> - The newly created company
   */
  create(createCompanyDto: CreateCompanyDto) {
    const company = this.repo.create({ ...createCompanyDto, status: true });
    return this.repo.save(company);
  }

  /**
   * Retrieves all companies accessible by a specific user
   * @param userId - ID of the user requesting the companies
   * @returns Promise<CompanyDto[]> - List of companies transformed to CompanyDto
   */
  async findAll(userId: string): Promise<CompanyDto[]> {
    const companies = await this.repo.findAllowed(userId);
    return companies;
  }

  /**
   * Retrieves a single company by its ID
   * @param id - ID of the company to retrieve
   * @returns Promise<CompanyDto> - The company transformed to CompanyDto
   */
  async findOne(id: string) {
    const company = await this.repo.findOneBy({ id });
    return company;
  }

  /**
   * Updates an existing company with new data
   * @param id - ID of the company to update
   * @param updateCompanyDto - Data to update the company with
   * @returns Promise<Company> - The updated company
   * @throws EntityNotFoundError if company doesn't exist
   */
  async update(id: string, updateCompanyDto: UpdateCompanyDto) {
    const entity = await this.repo.findOneByOrFail({ id });
    Object.assign(entity, updateCompanyDto);
    return this.repo.save(entity);
  }

  /**
   * Removes a company by its ID
   * @param id - ID of the company to remove
   * @returns Promise<Company> - The removed company
   * @throws EntityNotFoundError if company doesn't exist
   */
  async remove(id: string) {
    const entity = await this.repo.findOneByOrFail({ id });
    return this.repo.remove(entity);
  }
}
