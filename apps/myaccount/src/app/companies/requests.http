### Create a company
POST http://localhost:3000/api/companies
Content-Type: application/json

{
    "name": "My Company",
    "description": "Company Description",
}

### Get all companies
GET http://localhost:3000/api/companies

### Get one by id
GET http://localhost:3000/api/companies/21e5957f-24ed-4492-91b6-f45ea9c4de42

### Update a company
PATCH  http://localhost:3000/api/companies/21e5957f-24ed-4492-91b6-f45ea9c4de42
Content-Type: application/json

{
    "description": "Company Description 1"
}

### Removes one by id
DELETE http://localhost:3000/companies/2319b4aa