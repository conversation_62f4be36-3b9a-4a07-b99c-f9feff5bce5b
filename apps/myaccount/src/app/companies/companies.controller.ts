import { Auth<PERSON><PERSON>, MYACCOUNT_ADMIN_ROLES, Roles, RolesGuard, Serialize } from '@keeps-node-apis/@core';
import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { AuthenticatedUser } from 'nest-keycloak-connect';
import { CompaniesService } from './companies.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CompanyDto } from './dto/company.dto';

@ApiTags('Companies')
@Controller('companies')
@UseGuards(RolesGuard)
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Post()
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to access this resource' })
  @ApiOperation({ summary: 'Create a new company' })
  @ApiResponse({ status: 200, description: 'The company has been successfully created.', type: CompanyDto })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  create(@Body() body: CreateCompanyDto) {
    return this.companiesService.create(body);
  }

  @Get()
  @ApiOperation({ summary: 'Get all companies accessible by the authenticated user' })
  @ApiResponse({ status: 200, description: 'List of companies', type: [CompanyDto] })
  @Serialize(CompanyDto)
  findAll(@AuthenticatedUser() user: AuthUser) {
    return this.companiesService.findAll(user.sub);
  }

  @Get(':id')
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to access this resource' })
  @ApiParam({ name: 'id', description: 'Company ID' })
  @ApiResponse({ status: 200, description: 'The company details', type: CompanyDto })
  @ApiResponse({ status: 404, description: 'Company not found' })
  @Serialize(CompanyDto)
  findOne(@Param('id') id: string) {
    return this.companiesService.findOne(id);
  }

  @Patch(':id')
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to access this resource' })
  @ApiOperation({ summary: 'Update a company' })
  @ApiParam({ name: 'id', description: 'Company ID' })
  @ApiResponse({ status: 200, description: 'The updated company', type: CompanyDto })
  @ApiResponse({ status: 404, description: 'Company not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  update(@Param('id') id: string, @Body() body: UpdateCompanyDto) {
    return this.companiesService.update(id, body);
  }

  @Delete(':id')
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to access this resource' })
  @ApiOperation({ summary: 'Delete a company' })
  @ApiParam({ name: 'id', description: 'Company ID' })
  @ApiResponse({ status: 200, description: 'The company has been successfully deleted' })
  @ApiResponse({ status: 404, description: 'Company not found' })
  @Roles(MYACCOUNT_ADMIN_ROLES)
  remove(@Param('id') id: string) {
    return this.companiesService.remove(id);
  }
}
