import { PageOptionsWithSearchDto } from '@keeps-node-apis/@core';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { ApplicationServiceDto } from '../../application-services/presentation/dtos/application-service.dto';
import { WorkspaceDto } from '../../workspaces/dto/workspace.dto';

export class ApplicationDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier of the entity', example: '123e4567-e89b-12d3-a456-************' })
  @IsUUID()
  id: string;

  @Expose()
  @ApiProperty({ description: 'Name of the entity', example: 'My Application' })
  @IsString()
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Optional description of the entity',
    example: 'This is a sample application',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class ApplicatioListParamsDto extends PageOptionsWithSearchDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;
}

export class FilterOrderDto {
  @ApiPropertyOptional({
    description: 'Filter by name',
    example: 'My Application',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Text search (name or description)',
    example: 'app',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Field to order by',
    example: 'name',
  })
  @IsOptional()
  @IsString()
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Order direction (ASC or DESC)',
    example: 'ASC',
  })
  @IsOptional()
  @IsString()
  order?: 'ASC' | 'DESC';
}

export class WorkspaceApplicationDto extends WorkspaceDto {
  @Expose()
  @Type(() => ApplicationServiceDto)
  @ApiProperty({ description: 'List of services configured for the application within the current workspace' })
  services: ApplicationServiceDto[];
}
