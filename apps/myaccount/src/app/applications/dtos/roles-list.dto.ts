import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ApplicationDto } from './aplication.dto';

export class RoleListDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier of the entity', example: '123e4567-e89b-12d3-a456-************' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Name of the role', example: 'My Account Admin' })
  name: string;

  @Expose()
  @ApiProperty({ description: 'Role description', example: 'Have access to config own information' })
  description: string;

  @Expose()
  @ApiProperty({ description: 'Name of the entity', example: 'account_admin' })
  key: string;

  @Expose()
  @ApiProperty({ description: "Role's applicationo" })
  @Type(() => ApplicationDto)
  application: ApplicationDto;
}
