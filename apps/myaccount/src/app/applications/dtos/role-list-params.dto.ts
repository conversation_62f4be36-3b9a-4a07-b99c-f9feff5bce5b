import { PageOptionsWithSearchDto } from '@keeps-node-apis/@core';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString } from 'class-validator';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';

export class RoleListParamsDto extends PageOptionsWithSearchDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  key?: string;

  @ApiProperty({
    required: false,
    type: [UUID],
    example: '123e4567-e89b-12d3-a456-************,74000567-e89b-12d3-a456-4266141123e4,',
  })
  @IsOptional()
  @IsArray({})
  applicationId?: string[];
}
