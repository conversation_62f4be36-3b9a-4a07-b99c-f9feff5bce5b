import { Injectable } from '@nestjs/common';
import { Role } from '../../entities/role.entity';
import { RoleListParamsDto } from '../dtos/role-list-params.dto';
import { RolesRepository } from '../interfaces/roles.repository';
import { TenantService } from '@keeps-node-apis/@core';

@Injectable()
export class RolesService {
  constructor(
    private readonly roleRepository: RolesRepository,
    private readonly tenantService: TenantService,
  ) {}

  findAll(filter: RoleListParamsDto): Promise<Role[]> {
    return this.roleRepository.findAll(filter);
  }

  findEnableds(filter: RoleListParamsDto): Promise<Role[]> {
    const workspaceId = this.tenantService.getTenantId();
    return this.roleRepository.findAll(filter, workspaceId);
  }
}
