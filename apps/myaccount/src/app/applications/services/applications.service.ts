import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ApplicatioListParamsDto } from '../dtos/aplication.dto';
import { Application } from '../../entities/application.entity';
import { PageDto, paginate, TenantService } from '@keeps-node-apis/@core';
import ApplicationsRepository from '../repositories/application.repository';
import { WorkspacesRepository } from '../../workspaces/repositories/workspaces-repository';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(ApplicationsRepository)
    private readonly applicationRepo: ApplicationsRepository,
    @InjectRepository(WorkspacesRepository)
    private readonly workspacesRepository: WorkspacesRepository,
    private readonly tenantService: TenantService,
  ) {}

  findAll(listParams: ApplicatioListParamsDto): Promise<PageDto<Application>> {
    const queryBuilder = this.applicationRepo.findAllWithFilters(listParams);
    return paginate<Application>(queryBuilder, listParams, 'application');
  }

  getApplicationWorkspacesForUser(appId: string, userId: string) {
    return this.workspacesRepository.getWorkspacesByApplicationAndUser(userId, appId);
  }

  getApplicationWithRolesForWorkspace() {
    const workspaceId = this.tenantService.getTenantId();
    return this.applicationRepo.getApplicationsWithRolesForWorkspace(workspaceId);
  }
}
