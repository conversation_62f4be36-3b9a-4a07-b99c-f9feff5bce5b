import { ApplicationsService } from './applications.service';
import ApplicationsRepository from '../repositories/application.repository';
import { WorkspacesRepository } from '../../workspaces/repositories/workspaces-repository';
import { SelectQueryBuilder } from 'typeorm';
import { Chance } from 'chance';
import { TenantService } from '@keeps-node-apis/@core';

describe('ApplicationService', () => {
  let service: ApplicationsService;
  let mockApplicationRepo: jest.Mocked<ApplicationsRepository>;
  let mockWorkspaceRepo: jest.Mocked<WorkspacesRepository>;
  let tenantServiceMock: jest.Mocked<TenantService>;
  const chance = new Chance();

  beforeEach(async () => {
    mockApplicationRepo = {
      findAllWithFilters: jest.fn(),
      findOne: jest.fn(),
    } as unknown as jest.Mocked<ApplicationsRepository>;

    mockWorkspaceRepo = {
      getWorkspacesByApplicationAndUser: jest.fn(),
    } as unknown as jest.Mocked<WorkspacesRepository>;

    tenantServiceMock = {
      getTenantId: jest.fn().mockReturnValue('tenant-id'),
    } as unknown as jest.Mocked<TenantService>;

    service = new ApplicationsService(mockApplicationRepo, mockWorkspaceRepo, tenantServiceMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return paginated applications', async () => {
      const mockParams = { page: 1, perPage: 10, skip: 0 };
      const mockEntities = [{ id: '1', name: 'App 1' }];
      const mockQueryBuilder = {
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(1),
        getRawAndEntities: jest.fn().mockResolvedValue({
          entities: mockEntities,
          raw: mockEntities,
        }),
        orderBy: jest.fn().mockReturnThis(),
      } as unknown as SelectQueryBuilder<any>;

      mockApplicationRepo.findAllWithFilters.mockReturnValue(mockQueryBuilder);

      const result = await service.findAll(mockParams);

      expect(mockApplicationRepo.findAllWithFilters).toHaveBeenCalledWith(mockParams);
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(mockParams.skip);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(mockParams.perPage);
      expect(result).toBeDefined();
      expect(result.items).toEqual(mockEntities);
      expect(result.total).toBe(1);
    });
  });

  describe('getApplicationWorkspacesForUser', () => {
    it('should call getWorkspacesByApplicationAndUser in the workspaces repository', async () => {
      const appId = chance.guid();
      const userId = chance.guid();

      await service.getApplicationWorkspacesForUser(appId, userId);

      expect(mockWorkspaceRepo.getWorkspacesByApplicationAndUser).toHaveBeenCalledWith(userId, appId);
    });
  });
});
