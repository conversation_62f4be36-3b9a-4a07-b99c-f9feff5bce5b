import { Controller, Get, Param, Query, SerializeOptions } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApplicationsService } from '../services/applications.service';
import { ApplicatioListParamsDto, ApplicationDto, WorkspaceApplicationDto } from '../dtos/aplication.dto';
import {
  AuthUser,
  MYACCOUNT_ADMIN_ROLES,
  PageDto,
  PageDtoSwaggerResponse,
  Roles,
  Serialize,
  SerializePagination,
  SkipTenant,
} from '@keeps-node-apis/@core';
import { AuthenticatedUser } from 'nest-keycloak-connect';
import { ApplicationWithRolesDto } from '../dtos/application-with-roles.dto';

@ApiTags('Applications')
@Controller('applications')
export class ApplicationsController {
  constructor(private readonly applicationService: ApplicationsService) {}

  /**
   * Get all applications with optional filtering, pagination, and search.
   *
   * @param listParamsDto - Filters like name, search term, pagination, etc.
   * @returns Paginated list of applications.
   */
  @Get()
  @SerializePagination(ApplicationDto)
  @ApiOperation({ summary: 'Get all applications' })
  @PageDtoSwaggerResponse(ApplicationDto)
  async findAll(@Query() listParamsDto: ApplicatioListParamsDto): Promise<PageDto<ApplicationDto>> {
    return this.applicationService.findAll(listParamsDto);
  }

  /**
   * Get workspaces accessible by the authenticated user for a specific application.
   *
   * @param applicationId - ID of the application.
   * @param user - The authenticated user info injected by Keycloak.
   * @returns List of workspaces with configured services.
   */
  @Get(':id/workspaces')
  @SkipTenant()
  @Serialize(WorkspaceApplicationDto)
  @ApiOperation({ summary: 'Fetch user-accessible workspaces in an application' })
  @ApiOkResponse({
    description: 'The list of workspaces the user can access in the provided application',
    type: WorkspaceApplicationDto,
  })
  async getWorkspaces(@Param('id') applicationId: string, @AuthenticatedUser() user: AuthUser) {
    return this.applicationService.getApplicationWorkspacesForUser(applicationId, user.sub);
  }

  @Roles(MYACCOUNT_ADMIN_ROLES)
  @Get('workspace-applications')
  @SerializeOptions({ type: ApplicationWithRolesDto })
  @ApiOperation({ summary: 'Fetch the current workspace applications with their roles' })
  @ApiOkResponse({
    description: 'The list of applications available for this workspace with their roles',
    type: ApplicationWithRolesDto,
  })
  async getApplicationsWithRolesForWorkspace() {
    return this.applicationService.getApplicationWithRolesForWorkspace();
  }
}
