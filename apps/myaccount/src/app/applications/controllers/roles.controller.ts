import { Controller, Get, Query } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RoleListDto } from '../dtos/roles-list.dto';
import { RoleListParamsDto } from '../dtos/role-list-params.dto';
import { MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { RolesService } from '../services/roles.service';

@ApiTags('Roles')
@Controller('roles')
@Roles(MYACCOUNT_ADMIN_ROLES)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  /**
   * Get all roles across applications.
   *
   * @param listParamsDto - Filters like key, applicationId, pagination, etc.
   * @returns List of roles.
   */
  @Get('/all')
  @Serialize(RoleListDto)
  @ApiOperation({ summary: 'List all roles across all applications' })
  @ApiOkResponse({ description: 'List of roles', type: [RoleListDto] })
  findAll(@Query() listParamsDto: RoleListParamsDto) {
    return this.rolesService.findAll(listParamsDto);
  }

  /**
   * Get enabled roles for the current workspace (multi-tenant).
   *
   * @param listParamsDto - Filters like key, applicationId, pagination, etc.
   * @returns List of enabled roles for the current workspace.
   */
  @Get()
  @Serialize(RoleListDto)
  @ApiOperation({ summary: 'List enabled roles for current workspace' })
  @ApiOkResponse({ description: 'List of enabled roles', type: [RoleListDto] })
  findEnableds(@Query() listParamsDto: RoleListParamsDto) {
    return this.rolesService.findEnableds(listParamsDto);
  }
}
