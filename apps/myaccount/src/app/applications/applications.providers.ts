import { Provider } from '@nestjs/common';
import { RolesRepository } from './interfaces/roles.repository';
import { RolesTypeOrmRepository } from './repositories/roles.repository';
import { ApplicationsService } from './services/applications.service';
import { RolesService } from './services/roles.service';
import ApplicationsRepository from './repositories/application.repository';
import { WorkspacesRepository } from '../workspaces/repositories/workspaces-repository';

export const PROVIDERS: Provider[] = [
  {
    provide: RolesRepository,
    useClass: RolesTypeOrmRepository,
  },
  ApplicationsService,
  RolesService,
  ApplicationsRepository,
  WorkspacesRepository,
];
