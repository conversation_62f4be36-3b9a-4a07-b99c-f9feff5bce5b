import { Injectable } from '@nestjs/common';
import { ApplicatioListParamsDto } from '../dtos/aplication.dto';
import { Application } from '../../entities/application.entity';
import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export default class ApplicationsRepository extends Repository<Application> {
  constructor(
    @InjectRepository(Application)
    private readonly repository: Repository<Application>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  findAllWithFilters(filter: ApplicatioListParamsDto) {
    const query = this.createQueryBuilder('application');

    if (filter.name) query.where('application.name = :name', { name: filter.name });
    if (filter.search) {
      query.andWhere('(application.name ILIKE :search OR application.description ILIKE :search)', {
        search: `%${filter.search}%`,
      });
    }
    return query;
  }

  getApplicationsWithRolesForWorkspace(workspaceId: string) {
    return this.createQueryBuilder('application')
      .leftJoin('application.services', 'services')
      .leftJoin('services.serviceWorkspaces', 'service_workspace', 'service_workspace.workspaceId = :workspaceId', {
        workspaceId,
      })
      .leftJoinAndSelect('application.roles', 'roles')
      .getMany();
  }

  async findByIds(applicationIds: string[]): Promise<Application[]> {
    return this.find({ where: { id: In(applicationIds) } });
  }
}
