import { In, Repository, SelectQueryBuilder } from 'typeorm';
import { Role } from '../../entities/role.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleListParamsDto } from '../dtos/role-list-params.dto';
import { Inject, Injectable } from '@nestjs/common';
import { RolesRepository } from '../interfaces/roles.repository';
import { ConfigService } from '@nestjs/config';
import { Service } from '../../entities/service.entity';
import { ApplicationServicesRepository } from '../../application-services/interfaces/application-services-repository';

@Injectable()
export class RolesTypeOrmRepository implements RolesRepository {
  private readonly myAccountAppId: string;

  constructor(
    @InjectRepository(Role)
    readonly repository: Repository<Role>,
    private readonly configService: ConfigService,
    @Inject(ApplicationServicesRepository)
    private readonly applicationServicesRepository: ApplicationServicesRepository,
  ) {
    this.myAccountAppId = this.configService.getOrThrow('MYACCOUNT_ID');
  }

  async findAll(filter: RoleListParamsDto, workspaceId?: string): Promise<Role[]> {
    const query = this.repository.createQueryBuilder('role');

    this.applyFilterConditions(query, filter);

    if (workspaceId) {
      const applicationIds = await this.getApplicationIdsByWorkspace(workspaceId);
      this.applyWorkspaceApplicationFilter(query, applicationIds);
    }

    query.orderBy('role.name', 'ASC');
    query.leftJoinAndMapOne('role.application', 'role.application', 'application');

    return query.getMany();
  }

  private applyFilterConditions(query: SelectQueryBuilder<Role>, filter: RoleListParamsDto) {
    if (filter.key) {
      query.andWhere('role.key = :key', { key: filter.key });
    }

    if (filter.applicationId) {
      query.andWhere('role.applicationId = :applicationId', {
        applicationId: In(filter.applicationId),
      });
    }
  }

  private async getApplicationIdsByWorkspace(workspaceId: string): Promise<string[]> {
    const services: Service[] = await this.applicationServicesRepository.getApplicationServicesByWorkspace(workspaceId);
    return services.map((service) => service.applicationId);
  }

  private applyWorkspaceApplicationFilter(query: SelectQueryBuilder<Role>, applicationIds: string[]) {
    if (applicationIds.length) {
      query.andWhere('(role.applicationId IN (:...applicationIds) OR role.applicationId = :myAccountId)', {
        applicationIds,
        myAccountId: this.myAccountAppId,
      });
    } else {
      query.andWhere('role.applicationId = :myAccountId', { myAccountId: this.myAccountAppId });
    }
  }
}
