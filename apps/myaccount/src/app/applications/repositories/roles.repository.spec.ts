import { Repository } from 'typeorm';
import { Role } from '../../entities/role.entity';
import { RolesTypeOrmRepository } from './roles.repository';
import { RoleListParamsDto } from '../dtos/role-list-params.dto';
import { ConfigService } from '@nestjs/config';
import { ApplicationServicesRepository } from '../../application-services/interfaces/application-services-repository';
import { Service } from '../../entities/service.entity';

describe('RolesTypeOrmRepository', () => {
  let typeOrmRepository: jest.Mocked<Repository<Role>>;
  let configService: jest.Mocked<ConfigService>;
  let applicationServicesRepository: jest.Mocked<ApplicationServicesRepository>;
  let repository: RolesTypeOrmRepository;

  const mockRole = {
    id: '1',
    name: 'Admin',
    description: 'Administrator Role',
    applicationId: 'app1',
    status: true,
  } as Role;

  const mockAppId = 'my-account-app-id';

  beforeEach(() => {
    typeOrmRepository = {
      createQueryBuilder: jest.fn(),
    } as any;

    configService = {
      getOrThrow: jest.fn().mockReturnValue(mockAppId),
    } as any;

    applicationServicesRepository = {
      getApplicationServicesByWorkspace: jest.fn(),
    } as any;

    repository = new RolesTypeOrmRepository(
      typeOrmRepository,
      configService,
      applicationServicesRepository as ApplicationServicesRepository,
    );
  });

  describe('findAll', () => {
    it('should return roles filtered by key and applicationId', async () => {
      const filter: Partial<RoleListParamsDto> = { key: 'admin', applicationId: ['app1'] };

      const queryBuilder: any = {
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        leftJoinAndMapOne: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockRole]),
      };

      typeOrmRepository.createQueryBuilder.mockReturnValue(queryBuilder);

      const result = await repository.findAll(filter as RoleListParamsDto);

      expect(typeOrmRepository.createQueryBuilder).toHaveBeenCalledWith('role');
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('role.key = :key', { key: filter.key });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('role.applicationId = :applicationId', {
        applicationId: expect.anything(),
      });
      expect(queryBuilder.orderBy).toHaveBeenCalledWith('role.name', 'ASC');
      expect(queryBuilder.leftJoinAndMapOne).toHaveBeenCalledWith(
        'role.application',
        'role.application',
        'application',
      );
      expect(queryBuilder.getMany).toHaveBeenCalled();
      expect(result).toEqual([mockRole]);
    });

    it('should return roles without filters', async () => {
      const filter = {};

      const queryBuilder: any = {
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        leftJoinAndMapOne: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockRole]),
      };

      typeOrmRepository.createQueryBuilder.mockReturnValue(queryBuilder);

      const result = await repository.findAll(filter as RoleListParamsDto);

      expect(typeOrmRepository.createQueryBuilder).toHaveBeenCalledWith('role');
      expect(queryBuilder.andWhere).not.toHaveBeenCalledWith('role.key = :key', expect.anything());
      expect(queryBuilder.andWhere).not.toHaveBeenCalledWith('role.applicationId = :applicationId', expect.anything());
      expect(queryBuilder.orderBy).toHaveBeenCalledWith('role.name', 'ASC');
      expect(queryBuilder.leftJoinAndMapOne).toHaveBeenCalledWith(
        'role.application',
        'role.application',
        'application',
      );
      expect(queryBuilder.getMany).toHaveBeenCalled();
      expect(result).toEqual([mockRole]);
    });

    it('should filter roles by workspace applicationIds and myAccountAppId', async () => {
      const workspaceId = 'workspace1';

      const applicationServices = [{ applicationId: 'app1' }, { applicationId: 'app2' }];
      applicationServicesRepository.getApplicationServicesByWorkspace.mockResolvedValue(
        applicationServices as Service[],
      );

      const queryBuilder: any = {
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        leftJoinAndMapOne: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockRole]),
      };

      typeOrmRepository.createQueryBuilder.mockReturnValue(queryBuilder);

      const result = await repository.findAll({} as RoleListParamsDto, workspaceId);

      expect(applicationServicesRepository.getApplicationServicesByWorkspace).toHaveBeenCalledWith(workspaceId);
      expect(queryBuilder.andWhere).toHaveBeenCalledWith(
        '(role.applicationId IN (:...applicationIds) OR role.applicationId = :myAccountId)',
        {
          applicationIds: ['app1', 'app2'],
          myAccountId: mockAppId,
        },
      );
      expect(result).toEqual([mockRole]);
    });

    it('should filter roles by only myAccountAppId when no services found', async () => {
      const workspaceId = 'workspace1';

      applicationServicesRepository.getApplicationServicesByWorkspace.mockResolvedValue([]);

      const queryBuilder: any = {
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        leftJoinAndMapOne: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockRole]),
      };

      typeOrmRepository.createQueryBuilder.mockReturnValue(queryBuilder);

      const result = await repository.findAll({} as RoleListParamsDto, workspaceId);

      expect(applicationServicesRepository.getApplicationServicesByWorkspace).toHaveBeenCalledWith(workspaceId);
      expect(queryBuilder.andWhere).toHaveBeenCalledWith('role.applicationId = :myAccountId', {
        myAccountId: mockAppId,
      });
      expect(result).toEqual([mockRole]);
    });
  });
});
