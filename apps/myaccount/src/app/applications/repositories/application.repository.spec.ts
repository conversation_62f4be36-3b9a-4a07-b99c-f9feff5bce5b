import { Test, TestingModule } from '@nestjs/testing';
import { Application } from '../../entities/application.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import ApplicationsRepository from './application.repository';
import { ApplicatioListParamsDto } from '../dtos/aplication.dto';

const mockApplication = {
  id: '1',
  name: 'Test Application',
  description: 'Test Description',
};

describe('ApplicationRepository', () => {
  let repository: ApplicationsRepository;
  let mockRepo: Repository<Application>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationsRepository,
        {
          provide: getRepositoryToken(Application),
          useClass: Repository,
        },
      ],
    }).compile();

    repository = module.get<ApplicationsRepository>(ApplicationsRepository);
    mockRepo = module.get<Repository<Application>>(getRepositoryToken(Application));
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('findAllWithFilters', () => {
    it('should build query with filters', async () => {
      const filter: ApplicatioListParamsDto = { name: 'Test Application', search: 'Test' } as any;
      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockApplication]),
      };

      jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(queryBuilder as any);
      await repository.findAllWithFilters(filter);

      expect(queryBuilder.where).toHaveBeenCalledWith('application.name = :name', { name: filter.name });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith(
        '(application.name ILIKE :search OR application.description ILIKE :search)',
        { search: `%${filter.search}%` },
      );
    });
  });

  describe('findByIds', () => {
    it('should find applications by ids', async () => {
      jest.spyOn(mockRepo, 'find').mockResolvedValue([mockApplication as any]);
      repository.find = mockRepo.find.bind(mockRepo);

      const result = await repository.findByIds(['1']);
      expect(result).toEqual([mockApplication]);
      expect(mockRepo.find).toHaveBeenCalledWith({ where: { id: expect.any(Object) } });
    });
  });
});
