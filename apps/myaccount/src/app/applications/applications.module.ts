import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ApplicationServicesModule } from '../application-services/application-services.module';
import { EntitiesModule } from '../entities/entities.module';
import { PROVIDERS } from './applications.providers';
import { ApplicationsController } from './controllers/applications.controller';
import { RolesController } from './controllers/roles.controller';
import { ApplicationsService } from './services/applications.service';

@Module({
  imports: [EntitiesModule, ApplicationServicesModule, ConfigModule],
  controllers: [ApplicationsController, RolesController],
  providers: PROVIDERS,
  exports: [ApplicationsService],
})
export class ApplicationsModule {}
