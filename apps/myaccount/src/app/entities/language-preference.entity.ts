import { Column, Entity, Index, OneToMany } from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from './base-entity';

@Index('language_preference_pkey', ['id'], { unique: true })
@Entity('language_preference', { schema: 'public' })
export class LanguagePreference extends BaseEntity {
  @Column('character varying', { name: 'name', length: 200 })
  name: string;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @OneToMany(() => User, (user) => user.language)
  users: User[];
}
