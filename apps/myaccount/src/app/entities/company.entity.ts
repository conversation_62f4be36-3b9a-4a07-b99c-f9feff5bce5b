import { Column, Entity, Index, OneToMany } from 'typeorm';
import { Billing } from './billing.entity';
import { CompanyBillingPlan } from './company-billing-plan.entity';
import { Workspace } from './workspace.entity';
import { BaseEntity } from './base-entity';

@Index('company_pkey1', ['id'], { unique: true })
@Entity('company', { schema: 'public' })
export class Company extends BaseEntity {
  @Column('character varying', { name: 'name', length: 200 })
  name: string;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @Column('text', { name: 'address', nullable: true })
  address: string | null;

  @Column('character varying', { name: 'city', nullable: true, length: 200 })
  city: string | null;

  @Column('character varying', { name: 'state', nullable: true, length: 200 })
  state: string | null;

  @Column('character varying', {
    name: 'post_code',
    nullable: true,
    length: 200,
  })
  postCode: string | null;

  @Column('character varying', { name: 'country', nullable: true, length: 200 })
  country: string | null;

  @Column('text', { name: 'icon_url', nullable: true })
  iconUrl: string | null;

  @OneToMany(() => Billing, (billing) => billing.company)
  billings: Billing[];

  @OneToMany(() => CompanyBillingPlan, (companyBillingPlan) => companyBillingPlan.company)
  companyBillingPlans: CompanyBillingPlan[];

  @OneToMany(() => Workspace, (workspace) => workspace.company)
  workspaces: Workspace[];
}
