import { Column, <PERSON>ti<PERSON>, <PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Role } from './role.entity';
import { User } from './user.entity';
import { Workspace } from './workspace.entity';
import { BaseEntity } from './base-entity';
@Index('user_profile_application_company_pkey', ['id'], { unique: true })
@Index('user_profile_application_user_id_profile_applicat_470f09c4_uniq', ['roleId', 'userId', 'workspaceId'], {
  unique: true,
})
@Index('user_profile_application_c_profile_application_id_7405a125', ['roleId'], {})
@Index('user_profile_application_company_user_id_59aa32ff', ['userId'], {})
@Index('user_profile_application_company_company_id_ba45add2', ['workspaceId'], {})
@Entity('user_role_workspace', { schema: 'public' })
export class UserRoleWorkspace extends BaseEntity {
  @Column('boolean', { name: 'status' })
  status = true;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @Column('uuid', { name: 'role_id', unique: true })
  roleId: string;

  @Column('uuid', { name: 'user_id', unique: true })
  userId: string;

  @Column('boolean', { name: 'self_sign_up' })
  selfSignUp = false;

  @ManyToOne(() => Role, (role) => role.userRoleWorkspaces)
  @JoinColumn([{ name: 'role_id', referencedColumnName: 'id' }])
  role: Role;

  @ManyToOne(() => User, (user) => user.roles)
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: User;

  @ManyToOne(() => Workspace, (workspace) => workspace.userRoleWorkspaces)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
