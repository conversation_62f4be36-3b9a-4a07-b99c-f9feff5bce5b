import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { Application } from './application.entity';
import { UserRoleWorkspace } from './user-role-workspace.entity';
import { BaseEntity } from './base-entity';

@Index('profile_application_application_id_27f10135', ['applicationId'], {})
@Index('profile_application_pkey', ['id'], { unique: true })
@Entity('role', { schema: 'public' })
export class Role extends BaseEntity {
  @Column('character varying', { name: 'key', length: 200 })
  key: string;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @Column('uuid', { name: 'application_id' })
  applicationId: string;

  @Column('character varying', { name: 'name', nullable: true, length: 200 })
  name: string | null;

  @ManyToOne(() => Application, (application) => application.roles)
  @JoinColumn([{ name: 'application_id', referencedColumnName: 'id' }])
  application: Application;

  @OneToMany(() => UserRoleWorkspace, (userRoleWorkspace) => userRoleWorkspace.role)
  userRoleWorkspaces: UserRoleWorkspace[];
}
