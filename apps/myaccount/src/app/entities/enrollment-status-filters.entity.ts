import { Column, Entity, Index, OneToMany } from 'typeorm';
import { WorkspaceFilterSettings } from './workspace-filter-settings.entity';
import { BaseEntityId } from './base-entity-id';

@Index('enrollment_status_filters_pkey', ['id'], { unique: true })
@Index('enrollment_status_filters_status_code_c48cce88_like', ['statusCode'], {})
@Index('enrollment_status_filters_status_code_key', ['statusCode'], {
  unique: true,
})
@Entity('enrollment_status_filters', { schema: 'public' })
export class EnrollmentStatusFilters extends BaseEntityId {
  @Column('character varying', {
    name: 'status_code',
    unique: true,
    length: 50,
  })
  statusCode: string;

  @Column('character varying', { name: 'status_name', length: 100 })
  statusName: string;

  @OneToMany(() => WorkspaceFilterSettings, (workspaceFilterSettings) => workspaceFilterSettings.enrollmentStatusFilter)
  workspaceFilterSettings: WorkspaceFilterSettings[];
}
