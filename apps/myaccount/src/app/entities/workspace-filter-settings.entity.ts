import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Workspace } from './workspace.entity';
import { EnrollmentStatusFilters } from './enrollment-status-filters.entity';
import { BaseEntityWithDatesAt } from './base-entity-with-dates-at';

@Index('workspace_filter_settings_enrollment_status_filter_id_79b2f2f6', ['enrollmentStatusFilterId'], {})
@Index('workspace_filter_setting_workspace_id_enrollment__4023d118_uniq', ['enrollmentStatusFilterId', 'workspaceId'], {
  unique: true,
})
@Index('workspace_filter_settings_pkey', ['id'], { unique: true })
@Index('workspace_filter_settings_workspace_id_82265115', ['workspaceId'], {})
@Entity('workspace_filter_settings', { schema: 'public' })
export class WorkspaceFilterSettings extends BaseEntityWithDatesAt {
  @Column('boolean', { name: 'is_enabled' })
  isEnabled: boolean;

  @Column('uuid', { name: 'enrollment_status_filter_id', unique: true })
  enrollmentStatusFilterId: string;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @ManyToOne(
    () => EnrollmentStatusFilters,
    (enrollmentStatusFilters) => enrollmentStatusFilters.workspaceFilterSettings,
  )
  @JoinColumn([{ name: 'enrollment_status_filter_id', referencedColumnName: 'id' }])
  enrollmentStatusFilter: EnrollmentStatusFilters;

  @ManyToOne(() => Workspace, (workspace) => workspace.workspaceFilterSettings)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
