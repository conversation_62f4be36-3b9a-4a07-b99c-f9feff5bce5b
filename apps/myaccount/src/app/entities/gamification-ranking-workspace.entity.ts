import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseEntity } from './base-entity';
import { Service } from './service.entity';
import { Workspace } from './workspace.entity';

@Index('gamification_ranking_workspace_pkey', ['id'], { unique: true })
@Index('gamification_ranking_wor_service_id_workspace_id__5e74ea63_uniq', ['ranking', 'serviceId', 'workspaceId'], {
  unique: true,
})
@Index('gamification_ranking_workspace_service_id_bc622e08', ['serviceId'], {})
@Index('gamification_ranking_workspace_workspace_id_542f21d1', ['workspaceId'], {})
@Entity('gamification_ranking_workspace', { schema: 'public' })
export class GamificationRankingWorkspace extends BaseEntity {
  @Column('character varying', { name: 'ranking', unique: true, length: 30 })
  ranking: string;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @Column('uuid', { name: 'service_id', unique: true })
  serviceId: string;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @ManyToOne(() => Service, (service) => service.gamificationRankingWorkspaces)
  @JoinColumn([{ name: 'service_id', referencedColumnName: 'id' }])
  service: Service;

  @ManyToOne(() => Workspace, (workspace) => workspace.gamificationRankingWorkspaces)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
