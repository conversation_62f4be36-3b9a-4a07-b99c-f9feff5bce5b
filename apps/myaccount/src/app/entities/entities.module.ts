import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Application } from './application.entity';
import { Billing } from './billing.entity';
import { CompanyBillingPlan } from './company-billing-plan.entity';
import { Company } from './company.entity';
import { EmployeeInfo } from './employee-info.entity';
import { GamificationRankingWorkspace } from './gamification-ranking-workspace.entity';
import { IdpWorkspace } from './idp-workspace.entity';
import { JobFunction } from './job-function.entity';
import { Job } from './job.entity';
import { LanguagePreference } from './language-preference.entity';
import { Role } from './role.entity';
import { ServiceWorkspace } from './service-workspace.entity';
import { Service } from './service.entity';
import { UserIntegrationMessageHash } from './user-integration-message-hash.entity';
import { UserRoleWorkspace } from './user-role-workspace.entity';
import { User } from './user.entity';
import { WorkspaceCustomMenuItem } from './workspace-custom-menu-item.entity';
import { Workspace } from './workspace.entity';
import { EnrollmentStatusFilters } from './enrollment-status-filters.entity';
import { WorkspaceFilterSettings } from './workspace-filter-settings.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Application,
      Billing,
      CompanyBillingPlan,
      Company,
      EmployeeInfo,
      EnrollmentStatusFilters,
      GamificationRankingWorkspace,
      IdpWorkspace,
      JobFunction,
      Job,
      LanguagePreference,
      Role,
      Service,
      ServiceWorkspace,
      UserIntegrationMessageHash,
      UserRoleWorkspace,
      User,
      WorkspaceCustomMenuItem,
      WorkspaceFilterSettings,
      Workspace,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class EntitiesModule {}
