import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne, OneToMany } from 'typeorm';
import { Workspace } from './workspace.entity';
import { EmployeeInfo } from './employee-info.entity';
import { BaseEntity } from './base-entity';

@Index('job_pkey', ['id'], { unique: true })
@Index('job_name_workspace_id_dbe81680_uniq', ['name', 'workspaceId'], {
  unique: true,
})
@Index('job_workspace_id_353cd517', ['workspaceId'], {})
@Entity('job', { schema: 'public' })
export class Job extends BaseEntity {
  @Column('character varying', {
    name: 'name',
    nullable: true,
    unique: true,
    length: 200,
  })
  name: string | null;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.jobs)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;

  @OneToMany(() => EmployeeInfo, (employeeInfo) => employeeInfo.jobPosition)
  employeeInfos: EmployeeInfo[];
}
