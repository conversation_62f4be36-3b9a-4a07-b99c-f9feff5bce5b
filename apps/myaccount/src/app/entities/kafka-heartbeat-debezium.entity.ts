import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('kafka_heartbeat_debezium', { schema: 'public' })
export class KafkaHeartbeatDebezium {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  @Column('character varying', {
    name: 'heartbeat',
    nullable: true,
    length: 20,
  })
  heartbeat: string | null;

  @Column('timestamp without time zone', {
    name: 'timestamp',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
  })
  timestamp: Date | null;
}
