import { Column, Entity, Index } from 'typeorm';
import { BaseEntityWithoutUpdatedDate } from './base-entity-without-updated-date';

@Index('kafka_user_integration_log_pkey', ['id'], { unique: true })
@Entity('kafka_user_integration_log', { schema: 'public' })
export class KafkaUserIntegrationLog extends BaseEntityWithoutUpdatedDate {
  @Column('text', { name: 'title', nullable: true })
  title: string | null;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('text', { name: 'message_read', nullable: true })
  messageRead: string | null;

  @Column('uuid', { name: 'workspace_id', nullable: true })
  workspaceId: string | null;

  @Column('character varying', {
    name: 'batch_name',
    nullable: true,
    length: 500,
  })
  batchName: string | null;

  @Column('boolean', { name: 'is_error' })
  isError: boolean;
}
