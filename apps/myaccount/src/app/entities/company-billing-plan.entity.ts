import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Application } from './application.entity';
import { Company } from './company.entity';
import { BaseEntity } from './base-entity';

@Index('company_billing_plan_application_id_company_id_8f9d816f_uniq', ['applicationId', 'companyId'], { unique: true })
@Index('company_billing_plan_application_id_deed275d', ['applicationId'], {})
@Index('company_billing_plan_company_id_7dfaa71e', ['companyId'], {})
@Index('company_billing_plan_pkey', ['id'], { unique: true })
@Entity('company_billing_plan', { schema: 'public' })
export class CompanyBillingPlan extends BaseEntity {
  @Column('integer', { name: 'current_plan' })
  currentPlan: number;

  @Column('uuid', { name: 'application_id', unique: true })
  applicationId: string;

  @Column('uuid', { name: 'company_id', unique: true })
  companyId: string;

  @ManyToOne(() => Application, (application) => application.companyBillingPlans)
  @JoinColumn([{ name: 'application_id', referencedColumnName: 'id' }])
  application: Application;

  @ManyToOne(() => Company, (company) => company.companyBillingPlans)
  @JoinColumn([{ name: 'company_id', referencedColumnName: 'id' }])
  company: Company;
}
