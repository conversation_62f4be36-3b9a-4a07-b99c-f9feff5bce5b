import { BeforeInsert, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

export abstract class BaseEntity {
  @Column('uuid', { primary: true, name: 'id' })
  id: string;

  @CreateDateColumn({
    name: 'created_date',
    type: 'timestamp with time zone',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdDate!: Date | null;

  @UpdateDateColumn({
    name: 'updated_date',
    type: 'timestamp with time zone',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedDate!: Date | null;

  @BeforeInsert()
  genarate() {
    this.id = this.id || uuidv4();
    this.createdDate = this.createdDate ?? new Date();
    this.updatedDate = new Date();
  }
}
