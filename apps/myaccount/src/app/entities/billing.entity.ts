import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Application } from './application.entity';
import { BaseEntityWithoutUpdatedDate } from './base-entity-without-updated-date';
import { Company } from './company.entity';

@Index('billing_application_id_883a482a', ['applicationId'], {})
@Index('billing_company_id_1ca9e94c', ['companyId'], {})
@Index('billing_pkey', ['id'], { unique: true })
@Entity('billing', { schema: 'public' })
export class Billing extends BaseEntityWithoutUpdatedDate {
  @Column('integer', { name: 'monthly_plan' })
  monthlyPlan: number;

  @Column('integer', { name: 'used' })
  used: number;

  @Column('integer', { name: 'balance' })
  balance: number;

  @Column('timestamp with time zone', { name: 'start_date' })
  startDate: Date;

  @Column('timestamp with time zone', { name: 'end_date' })
  endDate: Date;

  @Column('uuid', { name: 'application_id' })
  applicationId: string;

  @Column('uuid', { name: 'company_id', nullable: true })
  companyId: string | null;

  @ManyToOne(() => Application, (application) => application.billings)
  @JoinColumn([{ name: 'application_id', referencedColumnName: 'id' }])
  application: Application;

  @ManyToOne(() => Company, (company) => company.billings)
  @JoinColumn([{ name: 'company_id', referencedColumnName: 'id' }])
  company: Company;
}
