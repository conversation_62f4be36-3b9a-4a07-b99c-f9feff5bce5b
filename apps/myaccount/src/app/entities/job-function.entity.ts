import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyTo<PERSON>ne, OneToMany } from 'typeorm';
import { BaseEntity } from './base-entity';
import { EmployeeInfo } from './employee-info.entity';
import { Workspace } from './workspace.entity';

@Index('job_function_pkey', ['id'], { unique: true })
@Index('job_function_name_workspace_id_4c767397_uniq', ['name', 'workspaceId'], { unique: true })
@Index('job_function_workspace_id_e0b8a9d7', ['workspaceId'], {})
@Entity('job_function', { schema: 'public' })
export class JobFunction extends BaseEntity {
  @Column('character varying', {
    name: 'name',
    nullable: true,
    unique: true,
    length: 200,
  })
  name: string | null;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.jobFunctions)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;

  @OneToMany(() => EmployeeInfo, (employeeInfo) => employeeInfo.jobFunction)
  employeeInfos: EmployeeInfo[];
}
