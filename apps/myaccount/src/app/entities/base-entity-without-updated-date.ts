import { BeforeInsert, Column, CreateDateColumn } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

export abstract class BaseEntityWithoutUpdatedDate {
  @Column('uuid', { primary: true, name: 'id' })
  id: string;

  @CreateDateColumn({
    name: 'created_date',
    type: 'timestamp with time zone',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdDate!: Date | null;

  @BeforeInsert()
  genarate() {
    this.id = uuidv4();
    this.createdDate = this.createdDate ?? new Date();
  }
}
