import { Column, <PERSON>ti<PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from './base-entity';
import { JobFunction } from './job-function.entity';
import { Job } from './job.entity';
import { User } from './user.entity';
import { Workspace } from './workspace.entity';

@Index('user_profile_workspace_pkey', ['id'], { unique: true })
@Index('user_profile_workspace_job_function_id_84b8bef7', ['jobFunctionId'], {})
@Index('user_profile_workspace_job_position_id_60088b77', ['jobPositionId'], {})
@Index('user_profile_workspace_user_id_workspace_id_5ab2fa27_uniq', ['userId', 'workspaceId'], { unique: true })
@Index('user_profile_workspace_user_id_02f38f50', ['userId'], {})
@Index('user_profile_workspace_workspace_id_40f75c1d', ['workspaceId'], {})
@Entity('user_profile_workspace', { schema: 'public' })
export class EmployeeInfo extends BaseEntity {
  @Column('character varying', {
    name: 'director',
    nullable: true,
    length: 200,
  })
  director: string | null;

  /**
   *  Após a migração adicionar a tradução correta a esse atributo que é Sub-directorate
   */
  @Column('character varying', { name: 'manager', nullable: true, length: 200 })
  manager: string | null;

  @Column('character varying', {
    name: 'area_of_activity',
    nullable: true,
    length: 300,
  })
  areaOfActivity: string | null;

  @Column('uuid', { name: 'user_id', unique: true })
  userId: string;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @Column('uuid', { name: 'job_position_id', nullable: true })
  jobPositionId: string | null;

  @Column('uuid', { name: 'job_function_id', nullable: true })
  jobFunctionId: string | null;

  @ManyToOne(() => JobFunction, (jobFunction) => jobFunction.employeeInfos)
  @JoinColumn([{ name: 'job_function_id', referencedColumnName: 'id' }])
  jobFunction: JobFunction;

  @ManyToOne(() => Job, (job) => job.employeeInfos)
  @JoinColumn([{ name: 'job_position_id', referencedColumnName: 'id' }])
  jobPosition: Job;

  @ManyToOne(() => User, (user) => user.employeeInfos)
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'id' }])
  user: User;

  @ManyToOne(() => Workspace, (workspace) => workspace.employeeInfos)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
