import { Column, Entity, Index, OneToMany } from 'typeorm';
import { Billing } from './billing.entity';
import { CompanyBillingPlan } from './company-billing-plan.entity';
import { Role } from './role.entity';
import { Service } from './service.entity';
import { BaseEntity } from './base-entity';

@Index('application_pkey', ['id'], { unique: true })
@Entity('application', { schema: 'public' })
export class Application extends BaseEntity {
  @Column('character varying', { name: 'name', length: 200 })
  name: string;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @OneToMany(() => Billing, (billing) => billing.application)
  billings: Billing[];

  @OneToMany(() => CompanyBillingPlan, (companyBillingPlan) => companyBillingPlan.application)
  companyBillingPlans: CompanyBillingPlan[];

  @OneToMany(() => Role, (role) => role.application)
  roles: Role[];

  @OneToMany(() => Service, (service) => service.application)
  services: Service[];
}
