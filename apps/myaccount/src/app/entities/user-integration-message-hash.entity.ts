import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from './base-entity';
@Index('user_integration_message_hash_pkey', ['id'], { unique: true })
@Entity('user_integration_message_hash', { schema: 'public' })
export class UserIntegrationMessageHash extends BaseEntity {
  @Column('text', { name: 'hash' })
  hash: string;

  @Column('uuid', { name: 'workspace_id' })
  workspaceId: string;

  @Column('text', { name: 'user_email' })
  userEmail: string;
}
