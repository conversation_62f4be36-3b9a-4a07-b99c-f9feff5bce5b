import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Workspace } from './workspace.entity';
import { BaseEntityId } from './base-entity-id';

@Index('idp_workspace_pkey', ['id'], { unique: true })
@Index('idp_workspace_idp_workspace_id_afc5c21e_uniq', ['idp', 'workspaceId'], {
  unique: true,
})
@Index('idp_workspace_workspace_id_599314dd', ['workspaceId'], {})
@Entity('idp_workspace', { schema: 'public' })
export class IdpWorkspace extends BaseEntityId {
  @Column('character varying', { name: 'idp', unique: true, length: 200 })
  idp: string;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.idpWorkspaces)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
