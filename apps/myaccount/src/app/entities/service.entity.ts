import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { GamificationRankingWorkspace } from './gamification-ranking-workspace.entity';
import { Application } from './application.entity';
import { ServiceWorkspace } from './service-workspace.entity';
import { BaseEntity } from './base-entity';

@Index('service_application_id_a9f6d464', ['applicationId'], {})
@Index('service_pkey', ['id'], { unique: true })
@Entity('service', { schema: 'public' })
export class Service extends BaseEntity {
  @Column('character varying', { name: 'name', length: 200 })
  name: string;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @Column('uuid', { name: 'application_id' })
  applicationId: string;

  @OneToMany(() => GamificationRankingWorkspace, (gamificationRankingWorkspace) => gamificationRankingWorkspace.service)
  gamificationRankingWorkspaces: GamificationRankingWorkspace[];

  @ManyToOne(() => Application, (application) => application.services)
  @JoinColumn([{ name: 'application_id', referencedColumnName: 'id' }])
  application: Application;

  @OneToMany(() => ServiceWorkspace, (serviceWorkspace) => serviceWorkspace.service)
  serviceWorkspaces: ServiceWorkspace[];
}
