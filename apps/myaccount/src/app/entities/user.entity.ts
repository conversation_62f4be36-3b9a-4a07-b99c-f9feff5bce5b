import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, <PERSON>ToOne, OneToMany } from 'typeorm';
import { LanguagePreference } from './language-preference.entity';
import { EmployeeInfo } from './employee-info.entity';
import { UserRoleWorkspace } from './user-role-workspace.entity';
import { BaseEntity } from './base-entity';
@Index('user_email_54dc62b2_like', ['email'], {})
@Index('user_email_54dc62b2_uniq', ['email'], { unique: true })
@Index('user_pkey', ['id'], { unique: true })
@Index('user_language_id_6dc62a7a', ['languageId'], {})
@Index('user_related_user_leader_id_3b6bd128', ['relatedUserLeaderId'], {})
@Entity('user', { schema: 'public' })
export class User extends BaseEntity {
  @Column('character varying', { name: 'name', nullable: true, length: 200 })
  name: string | null;

  @Column('character varying', {
    name: 'nickname',
    nullable: true,
    length: 200,
  })
  nickname: string | null;

  @Column('character varying', { name: 'email', unique: true, length: 200 })
  email: string;

  @Column('character varying', {
    name: 'secondary_email',
    nullable: true,
    length: 200,
  })
  secondaryEmail: string | null;

  @Column('character varying', { name: 'phone', nullable: true, length: 20 })
  phone: string | null;

  @Column('character varying', { name: 'gender', nullable: true, length: 200 })
  gender: string | null;

  @Column('date', { name: 'birthday', nullable: true })
  birthday: string | null;

  @Column('text', { name: 'address', nullable: true })
  address: string | null;

  @Column('text', { name: 'avatar', nullable: true })
  avatar: string | null;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @Column('uuid', { name: 'language_id', nullable: true })
  languageId: string | null;

  @Column('character varying', { name: 'country', nullable: true, length: 20 })
  country: string | null;

  @Column('character varying', { name: 'ein', nullable: true, length: 100 })
  ein: string | null;

  @Column('uuid', { name: 'related_user_leader_id', nullable: true })
  relatedUserLeaderId: string | null;

  @Column('boolean', { name: 'email_verified' })
  emailVerified: boolean;

  @Column('character varying', { name: 'time_zone', length: 200 })
  timeZone: string;

  @Column('date', { name: 'admission_date', nullable: true })
  admissionDate: string | null;

  @Column('character varying', {
    name: 'contract_type',
    nullable: true,
    length: 100,
  })
  contractType: string | null;

  @Column('character varying', { name: 'cpf', nullable: true, length: 15 })
  cpf: string | null;

  @Column('character varying', {
    name: 'education',
    nullable: true,
    length: 100,
  })
  education: string | null;

  @Column('character varying', {
    name: 'ethnicity',
    nullable: true,
    length: 100,
  })
  ethnicity: string | null;

  @Column('character varying', {
    name: 'hierarchical_level',
    nullable: true,
    length: 100,
  })
  hierarchicalLevel: string | null;

  @Column('character varying', {
    name: 'marital_status',
    nullable: true,
    length: 100,
  })
  maritalStatus: string | null;

  @Column('boolean', { name: 'is_user_integration' })
  isUserIntegration: boolean;

  @ManyToOne(() => LanguagePreference, (languagePreference) => languagePreference.users)
  @JoinColumn([{ name: 'language_id', referencedColumnName: 'id' }])
  language: LanguagePreference;

  @ManyToOne(() => User, (user) => user.users)
  @JoinColumn([{ name: 'related_user_leader_id', referencedColumnName: 'id' }])
  relatedUserLeader: User;

  @OneToMany(() => User, (user) => user.relatedUserLeader)
  users: User[];

  @OneToMany(() => EmployeeInfo, (employeeInfo) => employeeInfo.user)
  employeeInfos: EmployeeInfo[];

  @OneToMany(() => UserRoleWorkspace, (userRoleWorkspace) => userRoleWorkspace.user)
  roles: UserRoleWorkspace[];

  employeeInfo?: EmployeeInfo;
}
