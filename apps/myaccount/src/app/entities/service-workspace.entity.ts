import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseEntity } from './base-entity';
import { Service } from './service.entity';
import { Workspace } from './workspace.entity';

@Index('service_company_pkey', ['id'], { unique: true })
@Index('service_company_service_id_0bb52aab', ['serviceId'], {})
@Index('service_company_service_id_company_id_242d1e51_uniq', ['serviceId', 'workspaceId'], { unique: true })
@Index('service_company_company_id_2a36f3aa', ['workspaceId'], {})
@Entity('service_workspace', { schema: 'public' })
export class ServiceWorkspace extends BaseEntity {
  /**
   * Status field used for soft delete functionality.
   * true = active, false = deleted/inactive
   */
  @Column('boolean', { name: 'status', default: true })
  status: boolean;

  @Column('uuid', { name: 'workspace_id', unique: true })
  workspaceId: string;

  @Column('uuid', { name: 'service_id', unique: true })
  serviceId: string;

  @Column('character varying', {
    name: 'custom_url',
    nullable: true,
    length: 500,
  })
  customUrl: string | null;

  @ManyToOne(() => Service, (service) => service.serviceWorkspaces)
  @JoinColumn([{ name: 'service_id', referencedColumnName: 'id' }])
  service: Service;

  @ManyToOne(() => Workspace, (workspace) => workspace.serviceWorkspaces)
  @JoinColumn([{ name: 'workspace_id', referencedColumnName: 'id' }])
  workspace: Workspace;
}
