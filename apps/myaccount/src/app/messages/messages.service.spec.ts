import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import amqp, { ChannelWrapper } from 'amqp-connection-manager';
jest.mock('amqp-connection-manager');
import MessagesService from './messages.service';

describe('MessagesService', () => {
  let messagesService: MessagesService;
  let mockConfigService: Partial<ConfigService>;
  let mockChannelWrapper: Partial<jest.Mocked<ChannelWrapper>>;
  const mockedAmqp = amqp as jest.Mocked<typeof amqp>;

  beforeEach(async () => {
    mockConfigService = {
      get: jest.fn(),
    };

    mockChannelWrapper = {
      sendToQueue: jest.fn().mockResolvedValue(null),
    };

    mockedAmqp.connect.mockReturnValue({
      createChannel: (_queues) => {
        return mockChannelWrapper;
      },
    } as any);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: MessagesService,
          useValue: new MessagesService(mockConfigService as any, ['MAIN_QUEUE_KEY']),
        },
        {
          provide: Logger,
          useValue: {
            debug: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    messagesService = module.get<MessagesService>(MessagesService);
  });

  it('should send a message to the specified queue', async () => {
    const mockMessage = { data: 'test message' };
    const mockQueue = 'test-queue';

    messagesService.sendMessage(mockMessage, mockQueue);

    expect(mockChannelWrapper.sendToQueue).toHaveBeenCalledWith(mockQueue, mockMessage);
  });
});
