import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import amqp, { Channel, ChannelWrapper } from 'amqp-connection-manager';

@Injectable()
export default class MessagesService {
  private readonly logger = new Logger(MessagesService.name);
  readonly brokerUrl: string;
  private channelWrapper: ChannelWrapper;
  public queues: string[] = [];

  constructor(
    configService: ConfigService,
    private queueKeys: string[],
  ) {
    this.brokerUrl = configService.get('RABBITMQ_BROKER_URL');
    for (const queueKey of this.queueKeys) {
      const queueConfig = configService.get(queueKey);
      if (queueConfig) {
        this.queues.push(queueConfig);
      }
    }
    this.connect();
  }

  private connect() {
    const connection = amqp.connect([this.brokerUrl]);
    const queues = this.queues;
    this.channelWrapper = connection.createChannel({
      json: true,
      setup: function (channel: Channel) {
        for (const queue of queues) {
          channel.assertQueue(queue);
        }
        return;
      },
    });
    this.logger.debug(`connected on the rabbitmq broker ${this.brokerUrl}`);
  }

  sendMessage(message: object, queue: string) {
    this.channelWrapper
      .sendToQueue(queue, message)
      .then(() => {
        this.logger.debug(`message sent to queue ${queue}. message: ${JSON.stringify(message)}`);
      })
      .catch(() => {
        this.logger.error(`error to send message to queue ${queue}. message: ${JSON.stringify(message)}`);
      });
  }
}
