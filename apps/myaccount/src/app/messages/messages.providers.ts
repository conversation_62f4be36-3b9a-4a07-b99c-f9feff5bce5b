import { ConfigService } from '@nestjs/config';
import MessagesService from './messages.service';
import { EnvironmentEnums } from '@keeps-node-apis/@core';

const QUEUES = [EnvironmentEnums.QUEUE_SMARTZAP_COMPANY];

export const PROVIDERS = [
  {
    provide: MessagesService,
    useFactory: (configService: ConfigService) => new MessagesService(configService, QUEUES),
    inject: [ConfigService],
  },
];
