import { Body, Controller, Get, Headers, HttpStatus, Param, Patch } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GamificationService } from '../services/gamification.service';
import { GamificationWorkspaceDto } from '../dtos/gamification-workspace.dto';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { UpdateRankingDto } from '../dtos/update-ranking.dto';

/**
 * Controller responsible for handling gamification-related API endpoints.
 */
@ApiTags('gamification')
@Controller('gamification')
export class GamificationController {
  constructor(private readonly gamificationService: GamificationService) {}

  /**
   * Retrieves the gamification rankings for a specific workspace.
   * @param workspaceId The ID of the workspace, provided via the 'x-client' header
   * @returns A promise resolving to a GamificationWorkspaceDto containing the rankings and their statistics
   * @throws {HttpStatus.INTERNAL_SERVER_ERROR} When an internal error occurs while fetching rankings
   */
  @Get()
  @Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES, MYACCOUNT_ROLES.ACCOUNT_ADMIN])
  @ApiOperation({ summary: 'Retrieve gamification rankings for a workspace' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of gamification rankings retrieved successfully',
    type: GamificationWorkspaceDto,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error retrieving gamification rankings',
  })
  @Serialize(GamificationWorkspaceDto)
  async getRankings(@Headers('x-client') workspaceId: string): Promise<GamificationWorkspaceDto> {
    return this.gamificationService.getRankings(workspaceId);
  }

  /**
   * Updates the status of a specific gamification ranking within a workspace.
   * @param workspaceId The ID of the workspace, provided via the 'x-client' header
   * @param rankingId The ID of the ranking to update
   * @param updateRankingDto DTO containing the new status for the ranking
   * @returns A promise resolving to the updated ranking as a GamificationWorkspaceDto
   * @throws {HttpStatus.NOT_FOUND} When the ranking is not found
   * @throws {HttpStatus.BAD_REQUEST} When the ranking cannot be activated due to integrity issues
   * @throws {HttpStatus.INTERNAL_SERVER_ERROR} When an internal error occurs during the update
   */
  @Patch(':rankingId')
  @Roles([...KONQUEST_ADMIN_ROLES, ...MYACCOUNT_ADMIN_ROLES])
  @ApiOperation({ summary: 'Edit gamification ranking status' })
  @ApiBody({ type: UpdateRankingDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ranking status updated successfully',
    type: GamificationWorkspaceDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Ranking not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Ranking cannot be activated due to integrity constraints',
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  @Serialize(GamificationWorkspaceDto)
  async updateRanking(
    @Headers('x-client') workspaceId: string,
    @Param('rankingId') rankingId: string,
    @Body() updateRankingDto: UpdateRankingDto,
  ): Promise<GamificationWorkspaceDto> {
    const { status } = updateRankingDto;
    return this.gamificationService.editRanking(workspaceId, rankingId, status);
  }
}
