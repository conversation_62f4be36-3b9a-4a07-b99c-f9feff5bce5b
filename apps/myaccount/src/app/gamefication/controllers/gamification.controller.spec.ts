import { Test, TestingModule } from '@nestjs/testing';
import { GamificationController } from './gamification.controller';
import { GamificationService } from '../services/gamification.service';
import { GamificationWorkspaceDto } from '../dtos/gamification-workspace.dto';
import { UpdateRankingDto } from '../dtos/update-ranking.dto';
import { HttpStatus } from '@nestjs/common';

describe('GamificationController', () => {
  let controller: GamificationController;
  let gamificationService: GamificationService;

  const mockGamificationService = {
    getRankings: jest.fn(),
    editRanking: jest.fn(),
  };

  const mockWorkspaceDto: GamificationWorkspaceDto = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    ranking: 'general',
    status: true,
    can_enable: false,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GamificationController],
      providers: [
        {
          provide: GamificationService,
          useValue: mockGamificationService,
        },
      ],
    }).compile();

    controller = module.get<GamificationController>(GamificationController);
    gamificationService = module.get<GamificationService>(GamificationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getRankings', () => {
    it('should return gamification rankings successfully', async () => {
      const workspaceId = '123e4567-e89b-12d3-a456-426614174000';
      mockGamificationService.getRankings.mockResolvedValue(mockWorkspaceDto);

      const result = await controller.getRankings(workspaceId);

      expect(result).toEqual(mockWorkspaceDto);
      expect(gamificationService.getRankings).toHaveBeenCalledWith(workspaceId);
      expect(gamificationService.getRankings).toHaveBeenCalledTimes(1);
    });

    it('should throw an error when service fails', async () => {
      const workspaceId = '123e4567-e89b-12d3-a456-426614174000';
      const error = new Error('Internal server error');
      mockGamificationService.getRankings.mockRejectedValue(error);

      await expect(controller.getRankings(workspaceId)).rejects.toThrow(error);
      expect(gamificationService.getRankings).toHaveBeenCalledWith(workspaceId);
    });
  });

  describe('updateRanking', () => {
    it('should update ranking status successfully', async () => {
      const workspaceId = '123e4567-e89b-12d3-a456-426614174000';
      const rankingId = 'ranking-123';
      const updateRankingDto: UpdateRankingDto = { status: false };

      mockGamificationService.editRanking.mockResolvedValue(mockWorkspaceDto);

      const result = await controller.updateRanking(workspaceId, rankingId, updateRankingDto);

      expect(result).toEqual(mockWorkspaceDto);
      expect(gamificationService.editRanking).toHaveBeenCalledWith(workspaceId, rankingId, updateRankingDto.status);
      expect(gamificationService.editRanking).toHaveBeenCalledTimes(1);
    });

    it('should throw NOT_FOUND error when ranking doesnt exist', async () => {
      const workspaceId = '123e4567-e89b-12d3-a456-426614174000';
      const rankingId = 'ranking-123';
      const updateRankingDto: UpdateRankingDto = { status: false };

      const error = {
        status: HttpStatus.NOT_FOUND,
        message: 'Ranking not found',
      };
      mockGamificationService.editRanking.mockRejectedValue(error);

      await expect(controller.updateRanking(workspaceId, rankingId, updateRankingDto)).rejects.toMatchObject({
        status: HttpStatus.NOT_FOUND,
        message: 'Ranking not found',
      });
    });

    it('should throw FORBIDDEN error when ranking cannot be activated', async () => {
      const workspaceId = '123e4567-e89b-12d3-a456-426614174000';
      const rankingId = 'ranking-123';
      const updateRankingDto: UpdateRankingDto = { status: true };

      const error = {
        status: HttpStatus.FORBIDDEN,
        message: 'Ranking cannot be activated due to integrity constraints',
      };
      mockGamificationService.editRanking.mockRejectedValue(error);

      await expect(controller.updateRanking(workspaceId, rankingId, updateRankingDto)).rejects.toMatchObject({
        status: HttpStatus.FORBIDDEN,
        message: 'Ranking cannot be activated due to integrity constraints',
      });
    });

    it('should throw INTERNAL_SERVER_ERROR when service fails', async () => {
      const workspaceId = '123e4567-e89b-12d3-a456-426614174000';
      const rankingId = 'ranking-123';
      const updateRankingDto: UpdateRankingDto = { status: true };

      const error = new Error('Internal server error');
      mockGamificationService.editRanking.mockRejectedValue(error);

      await expect(controller.updateRanking(workspaceId, rankingId, updateRankingDto)).rejects.toThrow(error);
    });
  });
});
