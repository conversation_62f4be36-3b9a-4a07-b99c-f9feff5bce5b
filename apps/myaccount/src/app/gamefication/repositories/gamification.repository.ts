import { DataSource, Repository } from 'typeorm';
import { GamificationRankingWorkspace } from '../../entities/gamification-ranking-workspace.entity';
import IGamificationRankingWorkspaceRepository from './gamification-repository.interface';
import { Injectable } from '@nestjs/common';
import { EmployeeInfo } from '../../entities/employee-info.entity';

type RankingStats = {
  totalUsers: number;
  directorCount: number;
  managerCount: number;
  leaderCount: number;
  areaOfActivityCount: number;
};

@Injectable()
export class GamificationRankingWorkspaceRepository
  extends Repository<GamificationRankingWorkspace>
  implements IGamificationRankingWorkspaceRepository
{
  constructor(dataSource: DataSource) {
    super(GamificationRankingWorkspace, dataSource.createEntityManager());
  }

  private createStatsQuery(userProfileRepo: Repository<EmployeeInfo>, workspaceId: string) {
    return userProfileRepo
      .createQueryBuilder('user_profile')
      .select('COUNT(*)', 'totalUsers')
      .addSelect(this.createCountSubquery('director', workspaceId), 'directorCount')
      .addSelect(this.createCountSubquery('manager', workspaceId), 'managerCount')
      .addSelect(this.createCountSubquery('leader', workspaceId), 'leaderCount')
      .addSelect(this.createCountSubquery('activity_area', workspaceId), 'areaOfActivityCount')
      .where('user_profile.workspaceId = :workspaceId', { workspaceId });
  }

  private createCountSubquery(type: string, workspaceId: string) {
    return (subQuery: any) => {
      const query = subQuery
        .select('COUNT(*)')
        .from(EmployeeInfo, 'up')
        .where('up.workspaceId = :workspaceId', { workspaceId });

      switch (type) {
        case 'director':
          return query.andWhere('up.director IS NOT NULL');
        case 'manager':
          return query.andWhere('up.manager IS NOT NULL');
        case 'leader':
          return query.leftJoin('up.user', 'user').andWhere('user.relatedUserLeaderId IS NOT NULL');
        case 'activity_area':
          return query.andWhere('up.areaOfActivity IS NOT NULL');
      }
    };
  }

  private getAssociatedCount(stats: RankingStats, ranking: string): number {
    const countMap = {
      director: stats.directorCount,
      manager: stats.managerCount,
      leader: stats.leaderCount,
      activity_area: stats.areaOfActivityCount,
    };

    return countMap[ranking] ?? stats.totalUsers;
  }

  private calculateCanEnable(totalUsers: number, associatedCount: number): boolean {
    return totalUsers > 0 && associatedCount / totalUsers >= 0.7;
  }

  async getRankingsWithStats(workspaceId: string, userProfileRepo: Repository<EmployeeInfo>) {
    const [stats, rankings] = await Promise.all([
      this.createStatsQuery(userProfileRepo, workspaceId).getRawOne(),
      this.createQueryBuilder('ranking').where('ranking.workspaceId = :workspaceId', { workspaceId }).getMany(),
    ]);

    return rankings.map((ranking) => {
      const associatedCount = this.getAssociatedCount(stats, ranking.ranking);
      return {
        ...ranking,
        can_enable: this.calculateCanEnable(stats.totalUsers, associatedCount),
      };
    });
  }

  async getCanEnableStatus(
    workspaceId: string,
    ranking: GamificationRankingWorkspace,
    userProfileRepo: Repository<EmployeeInfo>,
  ): Promise<boolean> {
    const result = await this.createStatsQuery(userProfileRepo, workspaceId).getRawOne();
    const associatedCount = this.getAssociatedCount(result, ranking.ranking);

    return this.calculateCanEnable(result.totalUsers, associatedCount);
  }
}
