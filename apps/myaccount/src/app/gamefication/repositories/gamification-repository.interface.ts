import { Repository } from 'typeorm';
import { GamificationRankingWorkspace } from '../../entities/gamification-ranking-workspace.entity';
import { EmployeeInfo } from '../../entities/employee-info.entity';

export default interface IGamificationRankingWorkspaceRepository extends Repository<GamificationRankingWorkspace> {
  getRankingsWithStats(workspaceId: string, userProfileRepo: Repository<EmployeeInfo>);
  getCanEnableStatus(
    workspaceId: string,
    ranking: GamificationRankingWorkspace,
    userProfileRepo: Repository<EmployeeInfo>,
  );
}
