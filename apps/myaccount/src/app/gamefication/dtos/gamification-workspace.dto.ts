import { IsString, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class GamificationWorkspaceDto {
  @ApiProperty({
    description: 'Unique identifier of the workspace.',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @Expose()
  id: string;

  @ApiProperty({
    description: 'The ranking associated with the workspace.',
    example: 'general',
  })
  @IsString()
  @Expose()
  ranking: string;

  @ApiProperty({
    description: 'Indicates whether the ranking is active.',
    example: true,
  })
  @IsBoolean()
  @Expose()
  status: boolean;

  @ApiProperty({
    description: 'Indicates whether the ranking can be enabled.',
    example: false,
  })
  @IsBoolean()
  @Expose()
  can_enable?: boolean;
}
