import { Inject, Injectable } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { default as AppConfig } from '../../config/modules/app.config';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { GamificationRankingWorkspace } from '../../entities/gamification-ranking-workspace.entity';
import { GamificationWorkspaceDto } from '../dtos/gamification-workspace.dto';
import { RankingCannotBeActivatedError } from '../exceptions/ranking-cannot-be-activated.exception';
import { RankingNotFoundError } from '../exceptions/ranking-not-found.exception';
import IGamificationRankingWorkspaceRepository from '../repositories/gamification-repository.interface';
@Injectable()
export class GamificationService {
  constructor(
    @InjectRepository(GamificationRankingWorkspace)
    private readonly gamificationRepo: IGamificationRankingWorkspaceRepository,
    @InjectRepository(EmployeeInfo)
    private readonly userProfileRepo: Repository<EmployeeInfo>,
    @Inject(AppConfig.KEY)
    private config: ConfigType<typeof AppConfig>,
  ) {}

  /**
   * Retrieves gamification rankings for a given workspace with their associated statistics.
   *
   * @param workspaceId - The ID of the workspace to retrieve rankings for.
   * @returns A promise that resolves to a `GamificationWorkspaceDto` containing the rankings and their details.
   * @throws KeepsError - If any internal processing error occurs.
   */
  async getRankings(workspaceId: string): Promise<GamificationWorkspaceDto> {
    const rankings = await this.gamificationRepo.getRankingsWithStats(workspaceId, this.userProfileRepo);

    this.checkRankingIntegrity(rankings);

    return rankings;
  }

  /**
   * Edits the status of a specific ranking within a workspace.
   *
   * @param workspaceId - The ID of the workspace containing the ranking.
   * @param rankingId - The ID of the ranking to edit.
   * @param status - The new status of the ranking (true for active, false for inactive).
   * @returns A promise that resolves to the updated ranking as a `GamificationWorkspaceDto`.
   * @throws RankingNotFoundError - If the ranking with the given ID is not found.
   * @throws RankingCannotBeActivatedError - If the ranking cannot be activated due to integrity checks.
   */
  async editRanking(workspaceId: string, rankingId: string, status: boolean) {
    const ranking = await this.gamificationRepo.findOne({ where: { id: rankingId, workspaceId } });

    if (!ranking) {
      throw new RankingNotFoundError();
    }

    if (status) {
      const canEnable = await this.gamificationRepo.getCanEnableStatus(workspaceId, ranking, this.userProfileRepo);
      if (!canEnable) {
        throw new RankingCannotBeActivatedError(ranking.ranking);
      }
    }

    ranking.status = status;
    await this.gamificationRepo.save(ranking);
    return ranking;
  }

  /**
   * Creates default gamification rankings for a given workspace, initializing
   * them based on pre-defined choices.
   *
   * @param workspaceId - The ID of the workspace to create rankings for.
   * @returns A promise that resolves when the rankings are successfully created.
   */
  async createGamificationRankings(workspaceId: string) {
    const rankings = this.getRankingChoices();

    for (const [ranking, isGeneral] of rankings) {
      let rankingWorkspace = await this.gamificationRepo.findOne({ where: { workspaceId, ranking } });

      if (!rankingWorkspace) {
        rankingWorkspace = this.gamificationRepo.create({
          workspaceId,
          serviceId: process.env.GAMIFICATION_SERVICE_ID,
          ranking,
          status: isGeneral,
        });
        await this.gamificationRepo.save(rankingWorkspace);
      }
    }
  }

  /**
   * Removes all gamification rankings associated with a given workspace.
   *
   * @param workspaceId - The ID of the workspace to remove rankings from.
   * @returns A promise that resolves when the rankings are successfully deleted.
   */
  async removeGamificationRankings(workspaceId: string) {
    await this.gamificationRepo.delete({ workspaceId, serviceId: process.env.GAMIFICATION_SERVICE_ID });
  }

  private getRankingChoices(): [string, boolean][] {
    const RANKING_CHOICES = [
      ['general', true],
      ['specific', false],
    ];
    return RANKING_CHOICES.map(([ranking, isGeneral]) => [ranking, isGeneral] as [string, boolean]);
  }

  private async checkRankingIntegrity(rankings: GamificationRankingWorkspace[]) {
    for (const ranking of rankings) {
      const canEnable = await this.canEnableRanking(ranking);

      if (!canEnable && ranking.status) {
        ranking.status = false;
        await this.gamificationRepo.save(ranking);
      }
    }
  }

  private async canEnableRanking(ranking: GamificationRankingWorkspace): Promise<boolean> {
    return this.gamificationRepo.getCanEnableStatus(ranking.workspaceId, ranking, this.userProfileRepo);
  }
}
