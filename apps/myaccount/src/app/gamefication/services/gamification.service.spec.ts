import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { GamificationService } from './gamification.service';
import { GamificationRankingWorkspace } from '../../entities/gamification-ranking-workspace.entity';
import { Repository } from 'typeorm';
import { KeepsError } from '@keeps-node-apis/@core';
import { EmployeeInfo } from '../../entities/employee-info.entity';
import { default as AppConfig } from '../../config/modules/app.config';
describe('GamificationService', () => {
  let service: GamificationService;
  let gamificationRepo: jest.Mocked<any>;
  let userProfileRepo: jest.Mocked<Repository<EmployeeInfo>>;

  const mockWorkspaceId = 'workspace-123';
  const mockRankingId = 'ranking-123';

  const mockRankings = [
    {
      id: mockRankingId,
      workspaceId: mockWorkspaceId,
      ranking: 'general',
      status: true,
      serviceId: 'gamification-service',
    },
    {
      id: 'ranking-456',
      workspaceId: mockWorkspaceId,
      ranking: 'specific',
      status: false,
      serviceId: 'gamification-service',
    },
  ];

  beforeEach(async () => {
    gamificationRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      getRankingsWithStats: jest.fn(),
      getCanEnableStatus: jest.fn(),
    };

    userProfileRepo = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      getRawOne: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GamificationService,
        {
          provide: getRepositoryToken(GamificationRankingWorkspace),
          useValue: gamificationRepo,
        },
        {
          provide: getRepositoryToken(EmployeeInfo),
          useValue: userProfileRepo,
        },
        {
          provide: AppConfig.KEY,
          useValue: {
            gamificationUrl: 'https://gamification.com',
          },
        },
      ],
    }).compile();

    service = module.get<GamificationService>(GamificationService);
    process.env.GAMIFICATION_SERVICE_ID = 'gamification-service';
  });

  describe('getRankings', () => {
    it('should return rankings with stats', async () => {
      gamificationRepo.getRankingsWithStats.mockResolvedValue(mockRankings);

      const result = await service.getRankings(mockWorkspaceId);

      expect(result).toBeDefined();
      expect(gamificationRepo.getRankingsWithStats).toHaveBeenCalledWith(mockWorkspaceId, userProfileRepo);
    });

    it('should check ranking integrity and update status if necessary', async () => {
      const mockRanking = {
        id: mockRankingId,
        workspaceId: mockWorkspaceId,
        ranking: 'general',
        status: true,
        serviceId: 'gamification-service',
      };

      const mockRankingOk = {
        id: mockRankingId,
        workspaceId: mockWorkspaceId,
        ranking: 'general',
        status: false,
        serviceId: 'gamification-service',
      };

      jest.spyOn(service as any, 'canEnableRanking').mockResolvedValue(false);
      gamificationRepo.getRankingsWithStats.mockResolvedValue([mockRanking, mockRankingOk]);
      gamificationRepo.save.mockResolvedValue({ ...mockRanking, status: false });

      await service.getRankings(mockWorkspaceId);

      expect(gamificationRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockRanking,
          status: false,
        }),
      );
      expect(gamificationRepo.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('editRanking', () => {
    it('should throw error if ranking not found', async () => {
      gamificationRepo.findOne.mockResolvedValue(null);

      await expect(service.editRanking(mockWorkspaceId, mockRankingId, true)).rejects.toThrow(KeepsError);
    });

    it('should throw error if ranking cannot be enabled', async () => {
      gamificationRepo.findOne.mockResolvedValue(mockRankings[0]);
      gamificationRepo.getCanEnableStatus.mockResolvedValue(false);

      await expect(service.editRanking(mockWorkspaceId, mockRankingId, true)).rejects.toThrow(KeepsError);
    });

    it('should update ranking status when conditions are met', async () => {
      gamificationRepo.findOne.mockResolvedValue(mockRankings[0]);
      gamificationRepo.getCanEnableStatus.mockResolvedValue(true);
      gamificationRepo.save.mockResolvedValue({ ...mockRankings[0], status: true });

      const result = await service.editRanking(mockWorkspaceId, mockRankingId, true);

      expect(result).toBeDefined();
      expect(result.status).toBe(true);
      expect(gamificationRepo.save).toHaveBeenCalled();
    });
  });

  describe('createGamificationRankings', () => {
    it('should create rankings if they do not exist', async () => {
      gamificationRepo.findOne.mockResolvedValue(null);
      gamificationRepo.create.mockImplementation((data) => data);
      gamificationRepo.save.mockImplementation((data) => Promise.resolve(data));

      await service.createGamificationRankings(mockWorkspaceId);

      expect(gamificationRepo.create).toHaveBeenCalledTimes(2);
      expect(gamificationRepo.save).toHaveBeenCalledTimes(2);
    });

    it('should not create rankings if they already exist', async () => {
      gamificationRepo.findOne.mockResolvedValue(mockRankings[0]);

      await service.createGamificationRankings(mockWorkspaceId);

      expect(gamificationRepo.create).not.toHaveBeenCalled();
      expect(gamificationRepo.save).not.toHaveBeenCalled();
    });
  });

  describe('removeGamificationRankings', () => {
    it('should remove all rankings for workspace', async () => {
      await service.removeGamificationRankings(mockWorkspaceId);

      expect(gamificationRepo.delete).toHaveBeenCalledWith({
        workspaceId: mockWorkspaceId,
        serviceId: process.env.GAMIFICATION_SERVICE_ID,
      });
    });
  });
});
