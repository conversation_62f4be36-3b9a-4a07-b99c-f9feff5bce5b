import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EntitiesModule } from '../entities/entities.module';
import { GamificationController } from './controllers/gamification.controller';
import Providers from './gamification.providers';

@Module({
  imports: [EntitiesModule, ConfigModule],
  controllers: [GamificationController],
  providers: Providers,
  exports: [],
})
export class GamificationModule {}
