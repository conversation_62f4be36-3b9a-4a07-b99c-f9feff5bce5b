import { Provider } from '@nestjs/common';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

import { Workspace } from '../entities/workspace.entity';
import { WorkspacesRepository } from '../workspaces/repositories/workspaces-repository';
import { GamificationRankingWorkspaceRepository } from './repositories/gamification.repository';
import { GamificationRankingWorkspace } from '../entities/gamification-ranking-workspace.entity';
import { GamificationService } from './services/gamification.service';

const REPOSITORIES: Provider[] = [
  {
    provide: getRepositoryToken(Workspace),
    useFactory(datasource: DataSource) {
      return new WorkspacesRepository(datasource);
    },
    inject: [getDataSourceToken()],
  },
  {
    provide: getRepositoryToken(GamificationRankingWorkspace),
    useFactory(datasource: DataSource) {
      return new GamificationRankingWorkspaceRepository(datasource);
    },
    inject: [getDataSourceToken()],
  },
];

export default [GamificationService, ...REPOSITORIES];
