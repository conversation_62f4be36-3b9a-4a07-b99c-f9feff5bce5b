import { Injectable } from '@nestjs/common';
import UserRolesRepository from '../../users/repositories/user-roles.repository';
import { InternalDomainException } from '../domain/exceptions/internal.domain.exception';

@Injectable()
export class UserAccessCheckUseCase {
  constructor(private readonly userRolesWorkspace: UserRolesRepository) {}

  async execute(input: UserAccessCheckInput): Promise<boolean> {
    if (!input.roleIds && !input.applicationId) {
      throw new InternalDomainException(
        'Either applicationId or roleId must be provided.',
        'USER_ACCESS_CHECK_INVALID_INPUT',
      );
    }

    if (input.roleIds) {
      return this.userRolesWorkspace.userHasAnyRole(input.userId, input.workspaceId, input.roleIds);
    }
    return this.userRolesWorkspace.userHasRoleOnApplication(input.userId, input.workspaceId, input.applicationId);
  }
}

export class UserAccessCheckInput {
  workspaceId: string;
  userId: string;
  applicationId?: string;
  roleIds?: string[];
}
