import { UserAccessCheckUseCase } from './user-access-check.use-case';

describe('UserAccessCheckUseCase', () => {
  const mockUserRolesRepository = {
    userHasRoleOnApplication: jest.fn(),
  } as any;

  describe('execute', () => {
    const mockInput = {
      userId: '123',
      workspaceId: '456',
      applicationId: '789',
    };

    it('should return true when user has role on application', async () => {
      mockUserRolesRepository.userHasRoleOnApplication.mockResolvedValue(true);
      const useCase = new UserAccessCheckUseCase(mockUserRolesRepository);
      const result = await useCase.execute(mockInput);

      expect(result).toBe(true);
      expect(mockUserRolesRepository.userHasRoleOnApplication).toHaveBeenCalledWith(
        mockInput.userId,
        mockInput.workspaceId,
        mockInput.applicationId,
      );
    });

    it('should return false when user does not have role on application', async () => {
      mockUserRolesRepository.userHasRoleOnApplication.mockResolvedValue(false);
      const useCase = new UserAccessCheckUseCase(mockUserRolesRepository);
      const result = await useCase.execute(mockInput);

      expect(result).toBe(false);
      expect(mockUserRolesRepository.userHasRoleOnApplication).toHaveBeenCalledWith(
        mockInput.userId,
        mockInput.workspaceId,
        mockInput.applicationId,
      );
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      mockUserRolesRepository.userHasRoleOnApplication.mockRejectedValue(error);
      const useCase = new UserAccessCheckUseCase(mockUserRolesRepository);

      await expect(useCase.execute(mockInput)).rejects.toThrow(error);
      expect(mockUserRolesRepository.userHasRoleOnApplication).toHaveBeenCalledWith(
        mockInput.userId,
        mockInput.workspaceId,
        mockInput.applicationId,
      );
    });
  });
});
