import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class UserAccessCheckRequestDto {
  @ApiProperty({
    description: 'The ID of the workspace',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  workspaceId: string;

  @ApiProperty({
    description: 'The ID of the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'The ID of the application',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  applicationId: string;

  @ApiProperty({
    description:
      'A list of role IDs. The user will be considered as having access if they have at least one of the specified roles.',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  roleIds: string[];
}

export class UserAccessCheckResponseDto {
  @ApiProperty({
    description: 'Whether the user has access to the application',
    example: true,
  })
  @Expose()
  hasAccess: boolean;
}
