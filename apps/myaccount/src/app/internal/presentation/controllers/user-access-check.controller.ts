import { Serialize, SkipTenant } from '@keeps-node-apis/@core';
import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Resource, Scopes } from 'nest-keycloak-connect';
import { UserAccessCheckUseCase } from '../../application/user-access-check.use-case';
import { UserAccessCheckRequestDto, UserAccessCheckResponseDto } from '../dtos/user-access-check.dto';

@ApiTags('User Access Check')
@Controller('/internal/users')
@Resource('myaccount-internal-resources')
export class UserAccessCheckController {
  constructor(private readonly userAccessCheckUseCase: UserAccessCheckUseCase) {}

  @Post('access-check')
  @SkipTenant()
  @HttpCode(200)
  @Scopes('myaccount-internal')
  @Serialize(UserAccessCheckResponseDto)
  @ApiResponse({
    status: 200,
    description: 'User has access to the application',
    type: UserAccessCheckResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request',
  })
  async accessCheck(@Body() body: UserAccessCheckRequestDto): Promise<UserAccessCheckResponseDto> {
    const result = await this.userAccessCheckUseCase.execute(body);
    return { hasAccess: result };
  }
}
