import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';

import { UsersModule } from '../users/users.module';
import { INTERNAL_PROVIDERS } from './internal.providers';
import { UserAccessCheckController } from './presentation/controllers/user-access-check.controller';
import { UsersController } from './presentation/controllers/users.controller';
import { WorkspacesFilterSettingsController } from './presentation/controllers/workspaces-filter-settings.controller';
import { WorkspacesFilterSettingsModule } from '../workspaces-filter-settings/workspaces-filter-settings.module';
import { ApplicationServicesController } from './presentation/controllers/application-services.controller';
import { ApplicationServicesModule } from '../application-services/application-services.module';
import { InternalUsersGrpcController } from './presentation/controllers/users-grpc.controller';

@Module({
  imports: [UsersModule, WorkspacesFilterSettingsModule, ApplicationServicesModule, TypeOrmModule.forFeature([User])],
  controllers: [
    UserAccessCheckController,
    UsersController,
    WorkspacesFilterSettingsController,
    InternalUsersGrpcController,
    ApplicationServicesController,
  ],
  providers: INTERNAL_PROVIDERS,
})
export class InternalModule {}
