name: DEPLOY - <PERSON><PERSON>

on:
  workflow_call:
    inputs:
      tag-name:
        type: string

jobs:
  deploy:
    name: Deploying...
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          path: dist

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_EKS }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_EKS }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR Private
        uses: aws-actions/amazon-ecr-login@v1

      - name: Deploy to EKS
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG }}
          TAG: ${{ inputs.tag-name }}
        run: |
          echo "Install kubectl"
          curl -O https://s3.us-west-2.amazonaws.com/amazon-eks/1.27.4/2023-08-16/bin/linux/amd64/kubectl
          chmod +x ./kubectl
          mkdir -p $HOME/bin && cp ./kubectl $HOME/bin/kubectl && export PATH=$HOME/bin:$PATH
          kubectl version --short --client

          echo "Install aws-iam-authenticator"
          curl -Lo aws-iam-authenticator https://github.com/kubernetes-sigs/aws-iam-authenticator/releases/download/v0.5.9/aws-iam-authenticator_0.5.9_linux_amd64
          chmod +x ./aws-iam-authenticator
          mkdir -p $HOME/bin && cp ./aws-iam-authenticator $HOME/bin/aws-iam-authenticator && export PATH=$HOME/bin:$PATH

          echo "Set KUBECONFIG"
          echo "$KUBE_CONFIG_DATA" | base64 -d > /tmp/config
          export KUBECONFIG=/tmp/config

          declare -A images
          declare -A deployments
          registry=************.dkr.ecr.us-east-1.amazonaws.com

          apps=./dist
          deployments["corp"]="corp"
          deployments["search"]="search"
          deployments["notification"]="notification"
          deployments["user-registration"]="user-registration"
          deployments["regulatory-compliance"]="regulatory-compliance"
          deployments["myaccount"]="myaccount-v2"
          deployments["certificate-manager"]="certificate-manager"
          deployments["sisyphus"]="sisyphus"
          deployments["integration-gateway-alura"]="integration-gateway-alura"
          echo "##########################################################"
          echo "Deploying tag: $TAG"
          echo "##########################################################"
          if [ $TAG == "production" ]
          then
            deployments["caixa-api-gateway"]="caixa-api-gateway"
            deployments["caixa-worker"]="caixa-worker"
          fi


          images["corp"]="keeps-corp"
          images["search"]="keeps-search"
          images["notification"]="keeps-notification"
          images["user-registration"]="keeps-user-registration"
          images["regulatory-compliance"]="keeps-regulatory-compliance"
          images["myaccount"]="keeps-myaccount-v2"
          images["certificate-manager"]="keeps-certificate-manager"
          images["sisyphus"]="keeps-sisyphus"
          images["integration-gateway-alura"]="keeps-integration-gateway-alura"

          if [ $TAG == "production" ]
          then
            images["caixa-api-gateway"]="caixa-api-gateway"
            images["caixa-worker"]="caixa-worker"
          fi


          echo "Checking affected apps: $apps"
          if [ -d $apps ]
          then
            for path in $apps/*; do
              app=$(basename $path)
              imageName=$registry/${images[$app]}:$TAG
              echo "Checking app: $app deployment with image_name: $imageName"

              if [ ${images[$app]} ]
              then
                echo "Deploying app: $app"
                docker build -f apps/$app/.docker/prod-ci/Dockerfile -t "$imageName" .
                docker push "$imageName"
                kubectl rollout restart deployment/${deployments[$app]} -n $TAG
                sleep 3
              fi

            done
          fi
